/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 2v2", key: "tus03m" }],
  ["path", { d: "M13 8.129A4 4 0 0 1 15.873 11", key: "my0cn3" }],
  ["path", { d: "m19 5-1.256 1.256", key: "1yg6a6" }],
  ["path", { d: "M20 12h2", key: "1q8mjw" }],
  ["path", { d: "M9 8a5 5 0 1 0 7 7 7 7 0 1 1-7-7", key: "4qob92" }]
];
const SunMoon = createLucideIcon("sun-moon", __iconNode);

export { __iconNode, SunMoon as default };
//# sourceMappingURL=sun-moon.js.map
