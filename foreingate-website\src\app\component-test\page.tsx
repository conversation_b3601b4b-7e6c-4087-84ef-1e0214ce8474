'use client'

import { useState } from 'react'
import { SimpleWrapper } from '@/components/layout/simple-wrapper'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LoadingSpinner, LoadingOverlay, SkeletonCard } from '@/components/ui/loading'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { NewsletterSignup } from '@/components/ui/newsletter-signup'
import { WhatsAppWidget } from '@/components/ui/whatsapp-widget'
import { SmartChatbot } from '@/components/ui/smart-chatbot'
import { ImagePlaceholder, UniversityLogo, CampusImage } from '@/components/ui/image-placeholder'
import { useToast } from '@/components/ui/toast'
import { Search, Star, Heart, Download, Share } from 'lucide-react'

export default function ComponentTestPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedValue, setSelectedValue] = useState('')
  const [isChecked, setIsChecked] = useState(false)
  const { addToast } = useToast()

  const handleToastTest = (type: 'success' | 'error' | 'warning' | 'info') => {
    addToast({
      type,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Toast`,
      description: `This is a ${type} toast notification for testing purposes.`,
      duration: 5000
    })
  }

  const handleLoadingTest = () => {
    setIsLoading(true)
    setTimeout(() => setIsLoading(false), 3000)
  }

  return (
    <SimpleWrapper>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold">Component Test Page</h1>
            <p className="text-muted-foreground">
              Testing all UI components to ensure they work correctly
            </p>
          </div>

          {/* Basic UI Components */}
          <Card>
            <CardHeader>
              <CardTitle>Basic UI Components</CardTitle>
              <CardDescription>Testing buttons, inputs, and form elements</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Buttons */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Buttons</h3>
                <div className="flex flex-wrap gap-2">
                  <Button variant="default">Default</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                  <Button variant="destructive">Destructive</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm">Small</Button>
                  <Button size="default">Default</Button>
                  <Button size="lg">Large</Button>
                  <Button size="icon"><Star className="h-4 w-4" /></Button>
                </div>
              </div>

              {/* Form Elements */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Form Elements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input placeholder="Enter your name" />
                  <Input type="email" placeholder="Enter your email" />
                  <Textarea placeholder="Enter your message" />
                  <Select value={selectedValue} onValueChange={setSelectedValue}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="option1">Option 1</SelectItem>
                      <SelectItem value="option2">Option 2</SelectItem>
                      <SelectItem value="option3">Option 3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="test-checkbox" 
                    checked={isChecked}
                    onCheckedChange={setIsChecked}
                  />
                  <label htmlFor="test-checkbox">I agree to the terms and conditions</label>
                </div>
              </div>

              {/* Badges */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Badges</h3>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="outline">Outline</Badge>
                  <Badge variant="destructive">Destructive</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Loading Components */}
          <Card>
            <CardHeader>
              <CardTitle>Loading Components</CardTitle>
              <CardDescription>Testing loading states and spinners</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center space-y-2">
                  <h4 className="font-medium">Default Spinner</h4>
                  <LoadingSpinner />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="font-medium">Dots Spinner</h4>
                  <LoadingSpinner variant="dots" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="font-medium">Pulse Spinner</h4>
                  <LoadingSpinner variant="pulse" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="font-medium">Educational Spinner</h4>
                  <LoadingSpinner variant="educational" />
                </div>
              </div>
              
              <div className="space-y-4">
                <Button onClick={handleLoadingTest}>Test Loading Overlay</Button>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <SkeletonCard />
                  <SkeletonCard />
                  <SkeletonCard />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Theme and Language */}
          <Card>
            <CardHeader>
              <CardTitle>Theme and Language</CardTitle>
              <CardDescription>Testing theme toggle and language switcher</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-wrap gap-4 items-center">
                <div className="space-y-2">
                  <h4 className="font-medium">Theme Toggle</h4>
                  <ThemeToggle />
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Language Switcher (Default)</h4>
                  <LanguageSwitcher />
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Language Switcher (Compact)</h4>
                  <LanguageSwitcher variant="compact" />
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Language Switcher (Minimal)</h4>
                  <LanguageSwitcher variant="minimal" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Toast Notifications */}
          <Card>
            <CardHeader>
              <CardTitle>Toast Notifications</CardTitle>
              <CardDescription>Testing different types of toast notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Button onClick={() => handleToastTest('success')} variant="outline">
                  Success Toast
                </Button>
                <Button onClick={() => handleToastTest('error')} variant="outline">
                  Error Toast
                </Button>
                <Button onClick={() => handleToastTest('warning')} variant="outline">
                  Warning Toast
                </Button>
                <Button onClick={() => handleToastTest('info')} variant="outline">
                  Info Toast
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Image Components */}
          <Card>
            <CardHeader>
              <CardTitle>Image Components</CardTitle>
              <CardDescription>Testing image placeholders and university logos</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center space-y-2">
                  <h4 className="font-medium">University Logo</h4>
                  <UniversityLogo universityName="Eastern Mediterranean University" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="font-medium">Campus Image</h4>
                  <CampusImage campusName="EMU" className="w-full h-32" />
                </div>
                <div className="text-center space-y-2">
                  <h4 className="font-medium">General Placeholder</h4>
                  <ImagePlaceholder 
                    alt="Test image" 
                    width={200} 
                    height={128} 
                    type="general"
                    className="w-full h-32"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Newsletter Signup */}
          <Card>
            <CardHeader>
              <CardTitle>Newsletter Signup</CardTitle>
              <CardDescription>Testing newsletter subscription component</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <NewsletterSignup size="sm" />
                <NewsletterSignup size="md" />
                <NewsletterSignup size="lg" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Fixed Components */}
      <WhatsAppWidget phoneNumber="905392123456" />
      <SmartChatbot />
      
      {/* Loading Overlay */}
      <LoadingOverlay isLoading={isLoading} text="Testing loading overlay..." />
    </SimpleWrapper>
  )
}
