/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/simple-test/page";
exports.ids = ["app/simple-test/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/simple-test/page.tsx */ \"(rsc)/./src/app/simple-test/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'simple-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/simple-test/page\",\n        pathname: \"/simple-test\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(rsc)/./src/components/providers/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/translation-provider.tsx */ \"(rsc)/./src/components/providers/translation-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.tsx */ \"(rsc)/./src/components/ui/error-boundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.tsx */ \"(rsc)/./src/components/ui/toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/simple-wrapper.tsx */ \"(rsc)/./src/components/layout/simple-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q05pZGhhbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1QyU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNzaW1wbGUtd3JhcHBlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJTaW1wbGVXcmFwcGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBME0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNpbXBsZVdyYXBwZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxzaW1wbGUtd3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTmlkaGFsXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGZvcmVpbmdhdGVfZ3JvdXBlXFxmb3JlaW5nYXRlLXdlYnNpdGVcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"768acd2aba81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3NjhhY2QyYWJhODFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./src/components/providers/theme-provider.tsx\");\n/* harmony import */ var _components_providers_translation_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/providers/translation-provider */ \"(rsc)/./src/components/providers/translation-provider.tsx\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/toast */ \"(rsc)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_error_boundary__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/error-boundary */ \"(rsc)/./src/components/ui/error-boundary.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Foreingate Group - Study in Northern Cyprus\",\n    description: \"Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services including university admissions, visa support, and student housing.\",\n    keywords: [\n        \"Northern Cyprus\",\n        \"University\",\n        \"Study Abroad\",\n        \"Student Services\",\n        \"Education\",\n        \"Visa Support\"\n    ],\n    authors: [\n        {\n            name: \"Foreingate Group\"\n        }\n    ],\n    creator: \"Foreingate Group\",\n    publisher: \"Foreingate Group\",\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"https://foreingate.com\",\n        title: \"Foreingate Group - Study in Northern Cyprus\",\n        description: \"Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services.\",\n        siteName: \"Foreingate Group\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Foreingate Group - Study in Northern Cyprus\",\n        description: \"Your trusted partner for studying in Northern Cyprus. We help students achieve their academic dreams with comprehensive support services.\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen flex flex-col`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_boundary__WEBPACK_IMPORTED_MODULE_5__.ErrorBoundary, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_translation_provider__WEBPACK_IMPORTED_MODULE_3__.TranslationProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRTUE7QUFLQUM7QUFYaUI7QUFDK0M7QUFDWTtBQUM1QjtBQUNTO0FBWXhELE1BQU1LLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtRQUFDO1FBQW1CO1FBQWM7UUFBZ0I7UUFBb0I7UUFBYTtLQUFlO0lBQzVHQyxTQUFTO1FBQUM7WUFBRUMsTUFBTTtRQUFtQjtLQUFFO0lBQ3ZDQyxTQUFTO0lBQ1RDLFdBQVc7SUFDWEMsV0FBVztRQUNUQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMVixPQUFPO1FBQ1BDLGFBQWE7UUFDYlUsVUFBVTtJQUNaO0lBQ0FDLFNBQVM7UUFDUEMsTUFBTTtRQUNOYixPQUFPO1FBQ1BDLGFBQWE7SUFDZjtJQUNBYSxRQUFRO1FBQ05DLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxXQUFXO1lBQ1RGLE9BQU87WUFDUEMsUUFBUTtZQUNSLHFCQUFxQixDQUFDO1lBQ3RCLHFCQUFxQjtZQUNyQixlQUFlLENBQUM7UUFDbEI7SUFDRjtBQUNGLEVBQUU7QUFFYSxTQUFTRSxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0Msd0JBQXdCO2tCQUM1Qiw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHOUIsMkxBQWtCLENBQUMsQ0FBQyxFQUFFQyxnTUFBa0IsQ0FBQyx1Q0FBdUMsQ0FBQztzQkFFL0YsNEVBQUNJLHdFQUFhQTswQkFDWiw0RUFBQ0gsK0VBQWFBO29CQUNaOEIsV0FBVTtvQkFDVkMsY0FBYTtvQkFDYkMsWUFBWTtvQkFDWkMseUJBQXlCOzhCQUV6Qiw0RUFBQ2hDLDJGQUFtQkE7a0NBQ2xCLDRFQUFDQywrREFBYUE7c0NBQ1hzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFqQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxOaWRoYWxcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZm9yZWluZ2F0ZV9ncm91cGVcXGZvcmVpbmdhdGUtd2Vic2l0ZVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXJcIjtcbmltcG9ydCB7IFRyYW5zbGF0aW9uUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3Byb3ZpZGVycy90cmFuc2xhdGlvbi1wcm92aWRlclwiO1xuaW1wb3J0IHsgVG9hc3RQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdG9hc3RcIjtcbmltcG9ydCB7IEVycm9yQm91bmRhcnkgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2Vycm9yLWJvdW5kYXJ5XCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkZvcmVpbmdhdGUgR3JvdXAgLSBTdHVkeSBpbiBOb3J0aGVybiBDeXBydXNcIixcbiAgZGVzY3JpcHRpb246IFwiWW91ciB0cnVzdGVkIHBhcnRuZXIgZm9yIHN0dWR5aW5nIGluIE5vcnRoZXJuIEN5cHJ1cy4gV2UgaGVscCBzdHVkZW50cyBhY2hpZXZlIHRoZWlyIGFjYWRlbWljIGRyZWFtcyB3aXRoIGNvbXByZWhlbnNpdmUgc3VwcG9ydCBzZXJ2aWNlcyBpbmNsdWRpbmcgdW5pdmVyc2l0eSBhZG1pc3Npb25zLCB2aXNhIHN1cHBvcnQsIGFuZCBzdHVkZW50IGhvdXNpbmcuXCIsXG4gIGtleXdvcmRzOiBbXCJOb3J0aGVybiBDeXBydXNcIiwgXCJVbml2ZXJzaXR5XCIsIFwiU3R1ZHkgQWJyb2FkXCIsIFwiU3R1ZGVudCBTZXJ2aWNlc1wiLCBcIkVkdWNhdGlvblwiLCBcIlZpc2EgU3VwcG9ydFwiXSxcbiAgYXV0aG9yczogW3sgbmFtZTogXCJGb3JlaW5nYXRlIEdyb3VwXCIgfV0sXG4gIGNyZWF0b3I6IFwiRm9yZWluZ2F0ZSBHcm91cFwiLFxuICBwdWJsaXNoZXI6IFwiRm9yZWluZ2F0ZSBHcm91cFwiLFxuICBvcGVuR3JhcGg6IHtcbiAgICB0eXBlOiBcIndlYnNpdGVcIixcbiAgICBsb2NhbGU6IFwiZW5fVVNcIixcbiAgICB1cmw6IFwiaHR0cHM6Ly9mb3JlaW5nYXRlLmNvbVwiLFxuICAgIHRpdGxlOiBcIkZvcmVpbmdhdGUgR3JvdXAgLSBTdHVkeSBpbiBOb3J0aGVybiBDeXBydXNcIixcbiAgICBkZXNjcmlwdGlvbjogXCJZb3VyIHRydXN0ZWQgcGFydG5lciBmb3Igc3R1ZHlpbmcgaW4gTm9ydGhlcm4gQ3lwcnVzLiBXZSBoZWxwIHN0dWRlbnRzIGFjaGlldmUgdGhlaXIgYWNhZGVtaWMgZHJlYW1zIHdpdGggY29tcHJlaGVuc2l2ZSBzdXBwb3J0IHNlcnZpY2VzLlwiLFxuICAgIHNpdGVOYW1lOiBcIkZvcmVpbmdhdGUgR3JvdXBcIixcbiAgfSxcbiAgdHdpdHRlcjoge1xuICAgIGNhcmQ6IFwic3VtbWFyeV9sYXJnZV9pbWFnZVwiLFxuICAgIHRpdGxlOiBcIkZvcmVpbmdhdGUgR3JvdXAgLSBTdHVkeSBpbiBOb3J0aGVybiBDeXBydXNcIixcbiAgICBkZXNjcmlwdGlvbjogXCJZb3VyIHRydXN0ZWQgcGFydG5lciBmb3Igc3R1ZHlpbmcgaW4gTm9ydGhlcm4gQ3lwcnVzLiBXZSBoZWxwIHN0dWRlbnRzIGFjaGlldmUgdGhlaXIgYWNhZGVtaWMgZHJlYW1zIHdpdGggY29tcHJlaGVuc2l2ZSBzdXBwb3J0IHNlcnZpY2VzLlwiLFxuICB9LFxuICByb2JvdHM6IHtcbiAgICBpbmRleDogdHJ1ZSxcbiAgICBmb2xsb3c6IHRydWUsXG4gICAgZ29vZ2xlQm90OiB7XG4gICAgICBpbmRleDogdHJ1ZSxcbiAgICAgIGZvbGxvdzogdHJ1ZSxcbiAgICAgIFwibWF4LXZpZGVvLXByZXZpZXdcIjogLTEsXG4gICAgICBcIm1heC1pbWFnZS1wcmV2aWV3XCI6IFwibGFyZ2VcIixcbiAgICAgIFwibWF4LXNuaXBwZXRcIjogLTEsXG4gICAgfSxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZCBtaW4taC1zY3JlZW4gZmxleCBmbGV4LWNvbGB9XG4gICAgICA+XG4gICAgICAgIDxFcnJvckJvdW5kYXJ5PlxuICAgICAgICAgIDxUaGVtZVByb3ZpZGVyXG4gICAgICAgICAgICBhdHRyaWJ1dGU9XCJjbGFzc1wiXG4gICAgICAgICAgICBkZWZhdWx0VGhlbWU9XCJzeXN0ZW1cIlxuICAgICAgICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICAgICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFRyYW5zbGF0aW9uUHJvdmlkZXI+XG4gICAgICAgICAgICAgIDxUb2FzdFByb3ZpZGVyPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9Ub2FzdFByb3ZpZGVyPlxuICAgICAgICAgICAgPC9UcmFuc2xhdGlvblByb3ZpZGVyPlxuICAgICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgICAgPC9FcnJvckJvdW5kYXJ5PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJUaGVtZVByb3ZpZGVyIiwiVHJhbnNsYXRpb25Qcm92aWRlciIsIlRvYXN0UHJvdmlkZXIiLCJFcnJvckJvdW5kYXJ5IiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsImNyZWF0b3IiLCJwdWJsaXNoZXIiLCJvcGVuR3JhcGgiLCJ0eXBlIiwibG9jYWxlIiwidXJsIiwic2l0ZU5hbWUiLCJ0d2l0dGVyIiwiY2FyZCIsInJvYm90cyIsImluZGV4IiwiZm9sbG93IiwiZ29vZ2xlQm90IiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImF0dHJpYnV0ZSIsImRlZmF1bHRUaGVtZSIsImVuYWJsZVN5c3RlbSIsImRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/simple-test/page.tsx":
/*!**************************************!*\
  !*** ./src/app/simple-test/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_simple_wrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/simple-wrapper */ \"(rsc)/./src/components/layout/simple-wrapper.tsx\");\n\n\nfunction SimpleTestPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_simple_wrapper__WEBPACK_IMPORTED_MODULE_1__.SimpleWrapper, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold\",\n                        children: \"Simple Language Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"This is a simple test page to verify the website is working correctly.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg\",\n                                children: \"✅ Website is loading successfully\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg\",\n                                children: \"✅ Navigation is working\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg\",\n                                children: \"✅ Styling is applied correctly\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg\",\n                                children: \"✅ No webpack errors\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4\",\n                                children: \"Test Features:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2\",\n                                                children: \"Basic Functionality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-left space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Page routing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Component rendering\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 27,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ CSS styling\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 28,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Responsive design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 29,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 border rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2\",\n                                                children: \"Technical Health\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"text-left space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ No runtime errors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Clean console\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 36,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ Fast loading\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"✅ HTTPS enabled\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/\",\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: \"Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\app\\\\simple-test\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/simple-test/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/simple-wrapper.tsx":
/*!**************************************************!*\
  !*** ./src/components/layout/simple-wrapper.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SimpleWrapper: () => (/* binding */ SimpleWrapper)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SimpleWrapper = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SimpleWrapper() from the server but SimpleWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\layout\\simple-wrapper.tsx",
"SimpleWrapper",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\providers\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/translation-provider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/translation-provider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const TranslationProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TranslationProvider() from the server but TranslationProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\providers\\translation-provider.tsx",
"TranslationProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/error-boundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/error-boundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),
/* harmony export */   ErrorFallback: () => (/* binding */ ErrorFallback),
/* harmony export */   NetworkError: () => (/* binding */ NetworkError),
/* harmony export */   NotFound: () => (/* binding */ NotFound),
/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),
/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ErrorBoundary = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ErrorBoundary() from the server but ErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"ErrorBoundary",
);const ErrorFallback = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ErrorFallback() from the server but ErrorFallback is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"ErrorFallback",
);const useErrorHandler = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useErrorHandler() from the server but useErrorHandler is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"useErrorHandler",
);const withErrorBoundary = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call withErrorBoundary() from the server but withErrorBoundary is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"withErrorBoundary",
);const NetworkError = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NetworkError() from the server but NetworkError is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"NetworkError",
);const NotFound = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotFound() from the server but NotFound is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\error-boundary.tsx",
"NotFound",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),
/* harmony export */   toast: () => (/* binding */ toast),
/* harmony export */   useToast: () => (/* binding */ useToast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ToastProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\toast.tsx",
"ToastProvider",
);const useToast = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\toast.tsx",
"useToast",
);const toast = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\foreingate_groupe\\foreingate-website\\src\\components\\ui\\toast.tsx",
"toast",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(ssr)/./src/components/providers/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/translation-provider.tsx */ \"(ssr)/./src/components/providers/translation-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.tsx */ \"(ssr)/./src/components/ui/error-boundary.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.tsx */ \"(ssr)/./src/components/ui/toast.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q05pZGhhbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1QyU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3QlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1nZWlzdC1zYW5zJTVDJTIyJTJDJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyZ2Vpc3RTYW5zJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q05pZGhhbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1QyU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3RfTW9ubyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWdlaXN0LW1vbm8lNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJnZWlzdE1vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q3RoZW1lLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q3RyYW5zbGF0aW9uLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRyYW5zbGF0aW9uUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDTmlkaGFsJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2ZvcmVpbmdhdGVfZ3JvdXBlJTVDJTVDZm9yZWluZ2F0ZS13ZWJzaXRlJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDZXJyb3ItYm91bmRhcnkudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRXJyb3JCb3VuZGFyeSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNOaWRoYWwlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDZm9yZWluZ2F0ZV9ncm91cGUlNUMlNUNmb3JlaW5nYXRlLXdlYnNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBNk07QUFDN007QUFDQSxrTkFBeU47QUFDek47QUFDQSx3TEFBc007QUFDdE07QUFDQSxzS0FBNkwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFx0aGVtZS1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRyYW5zbGF0aW9uUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb3ZpZGVyc1xcXFx0cmFuc2xhdGlvbi1wcm92aWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkVycm9yQm91bmRhcnlcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXGVycm9yLWJvdW5kYXJ5LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXE5pZGhhbFxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxmb3JlaW5nYXRlX2dyb3VwZVxcXFxmb3JlaW5nYXRlLXdlYnNpdGVcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcdG9hc3QudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctranslation-provider.tsx%22%2C%22ids%22%3A%5B%22TranslationProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.tsx%22%2C%22ids%22%3A%5B%22ErrorBoundary%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/simple-wrapper.tsx */ \"(ssr)/./src/components/layout/simple-wrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q05pZGhhbCU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNmb3JlaW5nYXRlX2dyb3VwZSU1QyU1Q2ZvcmVpbmdhdGUtd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNzaW1wbGUtd3JhcHBlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJTaW1wbGVXcmFwcGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBME0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNpbXBsZVdyYXBwZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxOaWRoYWxcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcZm9yZWluZ2F0ZV9ncm91cGVcXFxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxzaW1wbGUtd3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CNidhal%5C%5CDocuments%5C%5Caugment-projects%5C%5Cforeingate_groupe%5C%5Cforeingate-website%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csimple-wrapper.tsx%22%2C%22ids%22%3A%5B%22SimpleWrapper%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/simple-wrapper.tsx":
/*!**************************************************!*\
  !*** ./src/components/layout/simple-wrapper.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleWrapper: () => (/* binding */ SimpleWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ SimpleWrapper auto */ \nfunction SimpleWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Foreingate Group\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"border-t\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-4 text-center text-muted-foreground\",\n                    children: \"\\xa9 2024 Foreingate Group. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\layout\\\\simple-wrapper.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvc2ltcGxlLXdyYXBwZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFRTyxTQUFTQSxjQUFjLEVBQUVDLFFBQVEsRUFBc0I7SUFDNUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRTt3QkFBR0YsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBR3ZDLDhEQUFDRztnQkFBS0gsV0FBVTswQkFDYkY7Ozs7OzswQkFFSCw4REFBQ007Z0JBQU9KLFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFBZ0U7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXZGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcc2ltcGxlLXdyYXBwZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFNpbXBsZVdyYXBwZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFNpbXBsZVdyYXBwZXIoeyBjaGlsZHJlbiB9OiBTaW1wbGVXcmFwcGVyUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJvcmRlci1iXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS00XCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPkZvcmVpbmdhdGUgR3JvdXA8L2gxPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvbWFpbj5cbiAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYm9yZGVyLXRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBweC00IHB5LTQgdGV4dC1jZW50ZXIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgwqkgMjAyNCBGb3JlaW5nYXRlIEdyb3VwLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiU2ltcGxlV3JhcHBlciIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwiaDEiLCJtYWluIiwiZm9vdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/simple-wrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkI7SUFDdEUscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXE5pZGhhbFxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxmb3JlaW5nYXRlX2dyb3VwZVxcZm9yZWluZ2F0ZS13ZWJzaXRlXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSAnbmV4dC10aGVtZXMnXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gJ25leHQtdGhlbWVzL2Rpc3QvdHlwZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/translation-provider.tsx":
/*!***********************************************************!*\
  !*** ./src/components/providers/translation-provider.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationProvider: () => (/* binding */ TranslationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* harmony import */ var _hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-translation */ \"(ssr)/./src/hooks/use-translation.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationProvider auto */ \n\n\n\nfunction TranslationProvider({ children, initialLocale = _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLocale }) {\n    const [locale, setLocaleState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLocale);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Hydration fix\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TranslationProvider.useEffect\": ()=>{\n            setIsClient(true);\n            // Load locale from localStorage or browser preference\n            const savedLocale = localStorage.getItem('locale');\n            const browserLocale = navigator.language.split('-')[0];\n            let preferredLocale = _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.defaultLocale;\n            if (savedLocale && (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(savedLocale)) {\n                preferredLocale = savedLocale;\n            } else if ((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(browserLocale)) {\n                preferredLocale = browserLocale;\n            }\n            if (preferredLocale !== locale) {\n                setLocaleState(preferredLocale);\n            }\n        }\n    }[\"TranslationProvider.useEffect\"], [\n        locale\n    ]);\n    const setLocale = (newLocale)=>{\n        if ((0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.isValidLocale)(newLocale)) {\n            setLocaleState(newLocale);\n            localStorage.setItem('locale', newLocale);\n            // Update document attributes\n            document.documentElement.lang = newLocale;\n            document.documentElement.dir = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(newLocale);\n            // Update page title if needed\n            const currentTitle = document.title;\n            if (currentTitle.includes('Foreingate')) {\n            // You can add locale-specific title updates here\n            }\n        }\n    };\n    // Update document attributes when locale changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TranslationProvider.useEffect\": ()=>{\n            if (isClient) {\n                document.documentElement.lang = locale;\n                document.documentElement.dir = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(locale);\n            }\n        }\n    }[\"TranslationProvider.useEffect\"], [\n        locale,\n        isClient\n    ]);\n    const translations = (0,_hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__.getTranslations)(locale);\n    const isRTL = (0,_lib_i18n__WEBPACK_IMPORTED_MODULE_2__.getLocaleDirection)(locale) === 'rtl';\n    const contextValue = {\n        locale,\n        setLocale,\n        t: translations,\n        isRTL\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_use_translation__WEBPACK_IMPORTED_MODULE_3__.TranslationContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\providers\\\\translation-provider.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/translation-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/error-boundary.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/error-boundary.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   ErrorFallback: () => (/* binding */ ErrorFallback),\n/* harmony export */   NetworkError: () => (/* binding */ NetworkError),\n/* harmony export */   NotFound: () => (/* binding */ NotFound),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,MessageCircle,RefreshCw!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,ErrorFallback,useErrorHandler,withErrorBoundary,NetworkError,NotFound auto */ \n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props), this.handleRetry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined,\n                errorInfo: undefined\n            });\n        }, this.handleGoHome = ()=>{\n            window.location.href = '/';\n        }, this.handleContactSupport = ()=>{\n            window.location.href = '/contact';\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error,\n            errorInfo\n        });\n        // Log error to console in development\n        if (true) {\n            console.error('Error Boundary caught an error:', error, errorInfo);\n        }\n        // Call custom error handler if provided\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\",\n                                    children: \"Oops! Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"We encountered an unexpected error. Don't worry, our team has been notified.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-semibold text-red-800 dark:text-red-400 mb-2\",\n                                    children: \"Error Details (Development Only):\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-xs text-red-700 dark:text-red-300 overflow-auto max-h-32\",\n                                    children: this.state.error.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleRetry,\n                                    className: \"w-full\",\n                                    variant: \"default\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleGoHome,\n                                    className: \"w-full\",\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Go to Homepage\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: this.handleContactSupport,\n                                    className: \"w-full\",\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Contact Support\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: [\n                                    \"Error ID: \",\n                                    Date.now().toString(36)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction ErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-5 h-5 text-red-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-red-800 dark:text-red-400\",\n                        children: \"Something went wrong\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-700 dark:text-red-300 mb-4\",\n                children: error.message || 'An unexpected error occurred'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: resetError,\n                variant: \"outline\",\n                size: \"sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    \"Try again\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n// Hook for error handling in functional components\nfunction useErrorHandler() {\n    return (error, errorInfo)=>{\n        console.error('Error caught by error handler:', error, errorInfo);\n        // In production, you might want to send this to an error reporting service\n        if (false) {}\n    };\n}\n// Higher-order component for wrapping components with error boundary\nfunction withErrorBoundary(Component, fallback) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            fallback: fallback,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n            lineNumber: 175,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Specific error components for different scenarios\nfunction NetworkError({ onRetry }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-12 h-12 text-yellow-500 mx-auto mb-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                children: \"Network Error\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: \"Unable to connect to our servers. Please check your internet connection.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: onRetry,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    \"Retry\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\nfunction NotFound({ title = \"Page Not Found\", description = \"The page you're looking for doesn't exist.\", showHomeButton = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-6xl font-bold text-gray-300 dark:text-gray-600 mb-4\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            showHomeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                onClick: ()=>window.location.href = '/',\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_MessageCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this),\n                    \"Go Home\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\error-boundary.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/error-boundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,toast auto */ \n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            ...toast,\n            id,\n            duration: toast.duration || 5000\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove after duration\n        if (newToast.duration > 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, newToast.duration);\n        }\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const clearToasts = ()=>{\n        setToasts([]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toasts,\n            addToast,\n            removeToast,\n            clearToasts\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return context;\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = useToast();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-[10000] space-y-2 max-w-sm w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastItem, {\n                    toast: toast,\n                    onRemove: removeToast\n                }, toast.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastItem({ toast, onRemove }) {\n    const getIcon = ()=>{\n        switch(toast.type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 16\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 16\n                }, this);\n            case 'info':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getBorderColor = ()=>{\n        switch(toast.type){\n            case 'success':\n                return 'border-l-green-500';\n            case 'error':\n                return 'border-l-red-500';\n            case 'warning':\n                return 'border-l-yellow-500';\n            case 'info':\n                return 'border-l-blue-500';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n        initial: {\n            opacity: 0,\n            x: 300,\n            scale: 0.3\n        },\n        animate: {\n            opacity: 1,\n            x: 0,\n            scale: 1\n        },\n        exit: {\n            opacity: 0,\n            x: 300,\n            scale: 0.5,\n            transition: {\n                duration: 0.2\n            }\n        },\n        className: `bg-white dark:bg-gray-800 border-l-4 ${getBorderColor()} rounded-lg shadow-lg p-4 max-w-sm w-full`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 w-0 flex-1\",\n                    children: [\n                        toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: toast.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: toast.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        toast.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toast.action.onClick,\n                                className: \"text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400\",\n                                children: toast.action.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-shrink-0 flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onRemove(toast.id),\n                        className: \"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\foreingate_groupe\\\\foreingate-website\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n// Convenience functions\nconst toast = {\n    success: (description, title, options)=>({\n            type: 'success',\n            title,\n            description,\n            ...options\n        }),\n    error: (description, title, options)=>({\n            type: 'error',\n            title,\n            description,\n            ...options\n        }),\n    warning: (description, title, options)=>({\n            type: 'warning',\n            title,\n            description,\n            ...options\n        }),\n    info: (description, title, options)=>({\n            type: 'info',\n            title,\n            description,\n            ...options\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-translation.ts":
/*!**************************************!*\
  !*** ./src/hooks/use-translation.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TranslationContext: () => (/* binding */ TranslationContext),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getNestedTranslation: () => (/* binding */ getNestedTranslation),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   pluralize: () => (/* binding */ pluralize),\n/* harmony export */   translateWithInterpolation: () => (/* binding */ translateWithInterpolation),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* harmony import */ var _locales_en__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/en */ \"(ssr)/./src/locales/en.ts\");\n/* harmony import */ var _locales_tr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/tr */ \"(ssr)/./src/locales/tr.ts\");\n/* harmony import */ var _locales_ar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/ar */ \"(ssr)/./src/locales/ar.ts\");\n/* harmony import */ var _locales_fr__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/fr */ \"(ssr)/./src/locales/fr.ts\");\n/* harmony import */ var _locales_es__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/locales/es */ \"(ssr)/./src/locales/es.ts\");\n/* __next_internal_client_entry_do_not_use__ TranslationContext,useTranslation,getTranslations,getNestedTranslation,translateWithInterpolation,pluralize,formatDate,formatNumber,formatCurrency,formatRelativeTime auto */ \n\n// Import all translations\n\n\n\n\n\n// Translation map\nconst translations = {\n    en: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tr: _locales_tr__WEBPACK_IMPORTED_MODULE_3__.tr,\n    ar: _locales_ar__WEBPACK_IMPORTED_MODULE_4__.ar,\n    fr: _locales_fr__WEBPACK_IMPORTED_MODULE_5__.fr,\n    es: _locales_es__WEBPACK_IMPORTED_MODULE_6__.es,\n    // Add more languages as they are created\n    es: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    de: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ru: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ja: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ko: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    it: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    no: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    da: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ro: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    et: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mt: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    cy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ga: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    is: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sq: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bs: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    me: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    be: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kk: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ky: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    uz: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ka: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hy: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    az: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ur: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    hi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ta: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    te: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ml: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    kn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    gu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    pa: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    or: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    as: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ne: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    si: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    my: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    th: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    km: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    vi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    id: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ms: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tl: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    haw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sm: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    to: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    fj: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    am: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ti: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    om: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    so: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    lg: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tw: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ig: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ha: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ff: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    wo: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    zu: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    xh: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    af: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    st: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ss: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ve: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ts: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nr: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    he: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    yi: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    jv: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    su: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mad: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ban: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bug: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    mak: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    min: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    ace: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bjn: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    bbc: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    nij: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    rej: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    sas: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en,\n    tet: _locales_en__WEBPACK_IMPORTED_MODULE_2__.en\n};\nconst TranslationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useTranslation() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(TranslationContext);\n    if (!context) {\n        throw new Error('useTranslation must be used within a TranslationProvider');\n    }\n    return context;\n}\n// Helper function to get translations for a specific locale\nfunction getTranslations(locale) {\n    return translations[locale] || translations[_lib_i18n__WEBPACK_IMPORTED_MODULE_1__.defaultLocale];\n}\n// Helper function to get nested translation value\nfunction getNestedTranslation(translations, key) {\n    const keys = key.split('.');\n    let value = translations;\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            return key // Return the key if translation not found\n            ;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n}\n// Translation function with interpolation support\nfunction translateWithInterpolation(template, values = {}) {\n    return template.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key)=>{\n        return values[key]?.toString() || match;\n    });\n}\n// Pluralization helper\nfunction pluralize(count, singular, plural) {\n    if (count === 1) {\n        return singular;\n    }\n    return plural || `${singular}s`;\n}\n// Date formatting helper\nfunction formatDate(date, locale, options) {\n    try {\n        return new Intl.DateTimeFormat(locale, options).format(date);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.DateTimeFormat('en', options).format(date);\n    }\n}\n// Number formatting helper\nfunction formatNumber(number, locale, options) {\n    try {\n        return new Intl.NumberFormat(locale, options).format(number);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', options).format(number);\n    }\n}\n// Currency formatting helper\nfunction formatCurrency(amount, locale, currency = 'USD') {\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency\n        }).format(amount);\n    } catch (error) {\n        // Fallback to English if locale is not supported\n        return new Intl.NumberFormat('en', {\n            style: 'currency',\n            currency\n        }).format(amount);\n    }\n}\n// Relative time formatting helper\nfunction formatRelativeTime(date, locale) {\n    try {\n        const rtf = new Intl.RelativeTimeFormat(locale, {\n            numeric: 'auto'\n        });\n        const now = new Date();\n        const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);\n        if (Math.abs(diffInSeconds) < 60) {\n            return rtf.format(diffInSeconds, 'second');\n        }\n        const diffInMinutes = Math.floor(diffInSeconds / 60);\n        if (Math.abs(diffInMinutes) < 60) {\n            return rtf.format(diffInMinutes, 'minute');\n        }\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (Math.abs(diffInHours) < 24) {\n            return rtf.format(diffInHours, 'hour');\n        }\n        const diffInDays = Math.floor(diffInHours / 24);\n        return rtf.format(diffInDays, 'day');\n    } catch (error) {\n        // Fallback to simple date formatting\n        return formatDate(date, locale);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-translation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   getLocaleDirection: () => (/* binding */ getLocaleDirection),\n/* harmony export */   getLocaleFontFamily: () => (/* binding */ getLocaleFontFamily),\n/* harmony export */   isValidLocale: () => (/* binding */ isValidLocale),\n/* harmony export */   languageNames: () => (/* binding */ languageNames),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n// Internationalization configuration\nconst defaultLocale = 'en';\nconst locales = [\n    'en',\n    'tr',\n    'ar',\n    'fr',\n    'es',\n    'de',\n    'ru',\n    'zh',\n    'ja',\n    'ko',\n    'pt',\n    'it',\n    'nl',\n    'sv',\n    'no',\n    'da',\n    'fi',\n    'pl',\n    'cs',\n    'hu',\n    'ro',\n    'bg',\n    'hr',\n    'sk',\n    'sl',\n    'et',\n    'lv',\n    'lt',\n    'mt',\n    'cy',\n    'ga',\n    'is',\n    'mk',\n    'sq',\n    'sr',\n    'bs',\n    'me',\n    'uk',\n    'be',\n    'kk',\n    'ky',\n    'uz',\n    'tg',\n    'tm',\n    'mn',\n    'ka',\n    'hy',\n    'az',\n    'fa',\n    'ur',\n    'hi',\n    'bn',\n    'ta',\n    'te',\n    'ml',\n    'kn',\n    'gu',\n    'pa',\n    'or',\n    'as',\n    'ne',\n    'si',\n    'my',\n    'th',\n    'lo',\n    'km',\n    'vi',\n    'id',\n    'ms',\n    'tl',\n    'haw',\n    'mi',\n    'sm',\n    'to',\n    'fj',\n    'sw',\n    'am',\n    'ti',\n    'om',\n    'so',\n    'rw',\n    'rn',\n    'lg',\n    'ak',\n    'tw',\n    'yo',\n    'ig',\n    'ha',\n    'ff',\n    'wo',\n    'sn',\n    'zu',\n    'xh',\n    'af',\n    'st',\n    'tn',\n    'ss',\n    've',\n    'ts',\n    'nr',\n    'he',\n    'yi',\n    'jv',\n    'su',\n    'mad',\n    'ban',\n    'bug',\n    'mak',\n    'min',\n    'ace',\n    'bjn',\n    'bbc',\n    'nij',\n    'rej',\n    'sas',\n    'tet'\n];\nconst languageNames = {\n    en: {\n        native: 'English',\n        english: 'English',\n        flag: '🇺🇸'\n    },\n    tr: {\n        native: 'Türkçe',\n        english: 'Turkish',\n        flag: '🇹🇷'\n    },\n    ar: {\n        native: 'العربية',\n        english: 'Arabic',\n        flag: '🇸🇦'\n    },\n    fr: {\n        native: 'Français',\n        english: 'French',\n        flag: '🇫🇷'\n    },\n    es: {\n        native: 'Español',\n        english: 'Spanish',\n        flag: '🇪🇸'\n    },\n    de: {\n        native: 'Deutsch',\n        english: 'German',\n        flag: '🇩🇪'\n    },\n    ru: {\n        native: 'Русский',\n        english: 'Russian',\n        flag: '🇷🇺'\n    },\n    zh: {\n        native: '中文',\n        english: 'Chinese',\n        flag: '🇨🇳'\n    },\n    ja: {\n        native: '日本語',\n        english: 'Japanese',\n        flag: '🇯🇵'\n    },\n    ko: {\n        native: '한국어',\n        english: 'Korean',\n        flag: '🇰🇷'\n    },\n    pt: {\n        native: 'Português',\n        english: 'Portuguese',\n        flag: '🇵🇹'\n    },\n    it: {\n        native: 'Italiano',\n        english: 'Italian',\n        flag: '🇮🇹'\n    },\n    nl: {\n        native: 'Nederlands',\n        english: 'Dutch',\n        flag: '🇳🇱'\n    },\n    sv: {\n        native: 'Svenska',\n        english: 'Swedish',\n        flag: '🇸🇪'\n    },\n    no: {\n        native: 'Norsk',\n        english: 'Norwegian',\n        flag: '🇳🇴'\n    },\n    da: {\n        native: 'Dansk',\n        english: 'Danish',\n        flag: '🇩🇰'\n    },\n    fi: {\n        native: 'Suomi',\n        english: 'Finnish',\n        flag: '🇫🇮'\n    },\n    pl: {\n        native: 'Polski',\n        english: 'Polish',\n        flag: '🇵🇱'\n    },\n    cs: {\n        native: 'Čeština',\n        english: 'Czech',\n        flag: '🇨🇿'\n    },\n    hu: {\n        native: 'Magyar',\n        english: 'Hungarian',\n        flag: '🇭🇺'\n    },\n    ro: {\n        native: 'Română',\n        english: 'Romanian',\n        flag: '🇷🇴'\n    },\n    bg: {\n        native: 'Български',\n        english: 'Bulgarian',\n        flag: '🇧🇬'\n    },\n    hr: {\n        native: 'Hrvatski',\n        english: 'Croatian',\n        flag: '🇭🇷'\n    },\n    sk: {\n        native: 'Slovenčina',\n        english: 'Slovak',\n        flag: '🇸🇰'\n    },\n    sl: {\n        native: 'Slovenščina',\n        english: 'Slovenian',\n        flag: '🇸🇮'\n    },\n    et: {\n        native: 'Eesti',\n        english: 'Estonian',\n        flag: '🇪🇪'\n    },\n    lv: {\n        native: 'Latviešu',\n        english: 'Latvian',\n        flag: '🇱🇻'\n    },\n    lt: {\n        native: 'Lietuvių',\n        english: 'Lithuanian',\n        flag: '🇱🇹'\n    },\n    mt: {\n        native: 'Malti',\n        english: 'Maltese',\n        flag: '🇲🇹'\n    },\n    cy: {\n        native: 'Cymraeg',\n        english: 'Welsh',\n        flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿'\n    },\n    ga: {\n        native: 'Gaeilge',\n        english: 'Irish',\n        flag: '🇮🇪'\n    },\n    is: {\n        native: 'Íslenska',\n        english: 'Icelandic',\n        flag: '🇮🇸'\n    },\n    mk: {\n        native: 'Македонски',\n        english: 'Macedonian',\n        flag: '🇲🇰'\n    },\n    sq: {\n        native: 'Shqip',\n        english: 'Albanian',\n        flag: '🇦🇱'\n    },\n    sr: {\n        native: 'Српски',\n        english: 'Serbian',\n        flag: '🇷🇸'\n    },\n    bs: {\n        native: 'Bosanski',\n        english: 'Bosnian',\n        flag: '🇧🇦'\n    },\n    me: {\n        native: 'Crnogorski',\n        english: 'Montenegrin',\n        flag: '🇲🇪'\n    },\n    uk: {\n        native: 'Українська',\n        english: 'Ukrainian',\n        flag: '🇺🇦'\n    },\n    be: {\n        native: 'Беларуская',\n        english: 'Belarusian',\n        flag: '🇧🇾'\n    },\n    kk: {\n        native: 'Қазақша',\n        english: 'Kazakh',\n        flag: '🇰🇿'\n    },\n    ky: {\n        native: 'Кыргызча',\n        english: 'Kyrgyz',\n        flag: '🇰🇬'\n    },\n    uz: {\n        native: 'Oʻzbekcha',\n        english: 'Uzbek',\n        flag: '🇺🇿'\n    },\n    tg: {\n        native: 'Тоҷикӣ',\n        english: 'Tajik',\n        flag: '🇹🇯'\n    },\n    tm: {\n        native: 'Türkmençe',\n        english: 'Turkmen',\n        flag: '🇹🇲'\n    },\n    mn: {\n        native: 'Монгол',\n        english: 'Mongolian',\n        flag: '🇲🇳'\n    },\n    ka: {\n        native: 'ქართული',\n        english: 'Georgian',\n        flag: '🇬🇪'\n    },\n    hy: {\n        native: 'Հայերեն',\n        english: 'Armenian',\n        flag: '🇦🇲'\n    },\n    az: {\n        native: 'Azərbaycan',\n        english: 'Azerbaijani',\n        flag: '🇦🇿'\n    },\n    fa: {\n        native: 'فارسی',\n        english: 'Persian',\n        flag: '🇮🇷'\n    },\n    ur: {\n        native: 'اردو',\n        english: 'Urdu',\n        flag: '🇵🇰'\n    },\n    hi: {\n        native: 'हिन्दी',\n        english: 'Hindi',\n        flag: '🇮🇳'\n    },\n    bn: {\n        native: 'বাংলা',\n        english: 'Bengali',\n        flag: '🇧🇩'\n    },\n    ta: {\n        native: 'தமிழ்',\n        english: 'Tamil',\n        flag: '🇱🇰'\n    },\n    te: {\n        native: 'తెలుగు',\n        english: 'Telugu',\n        flag: '🇮🇳'\n    },\n    ml: {\n        native: 'മലയാളം',\n        english: 'Malayalam',\n        flag: '🇮🇳'\n    },\n    kn: {\n        native: 'ಕನ್ನಡ',\n        english: 'Kannada',\n        flag: '🇮🇳'\n    },\n    gu: {\n        native: 'ગુજરાતી',\n        english: 'Gujarati',\n        flag: '🇮🇳'\n    },\n    pa: {\n        native: 'ਪੰਜਾਬੀ',\n        english: 'Punjabi',\n        flag: '🇮🇳'\n    },\n    or: {\n        native: 'ଓଡ଼ିଆ',\n        english: 'Odia',\n        flag: '🇮🇳'\n    },\n    as: {\n        native: 'অসমীয়া',\n        english: 'Assamese',\n        flag: '🇮🇳'\n    },\n    ne: {\n        native: 'नेपाली',\n        english: 'Nepali',\n        flag: '🇳🇵'\n    },\n    si: {\n        native: 'සිංහල',\n        english: 'Sinhala',\n        flag: '🇱🇰'\n    },\n    my: {\n        native: 'မြန်မာ',\n        english: 'Burmese',\n        flag: '🇲🇲'\n    },\n    th: {\n        native: 'ไทย',\n        english: 'Thai',\n        flag: '🇹🇭'\n    },\n    lo: {\n        native: 'ລາວ',\n        english: 'Lao',\n        flag: '🇱🇦'\n    },\n    km: {\n        native: 'ខ្មែរ',\n        english: 'Khmer',\n        flag: '🇰🇭'\n    },\n    vi: {\n        native: 'Tiếng Việt',\n        english: 'Vietnamese',\n        flag: '🇻🇳'\n    },\n    id: {\n        native: 'Bahasa Indonesia',\n        english: 'Indonesian',\n        flag: '🇮🇩'\n    },\n    ms: {\n        native: 'Bahasa Melayu',\n        english: 'Malay',\n        flag: '🇲🇾'\n    },\n    tl: {\n        native: 'Filipino',\n        english: 'Filipino',\n        flag: '🇵🇭'\n    },\n    haw: {\n        native: 'ʻŌlelo Hawaiʻi',\n        english: 'Hawaiian',\n        flag: '🏝️'\n    },\n    mi: {\n        native: 'Te Reo Māori',\n        english: 'Maori',\n        flag: '🇳🇿'\n    },\n    sm: {\n        native: 'Gagana Samoa',\n        english: 'Samoan',\n        flag: '🇼🇸'\n    },\n    to: {\n        native: 'Lea Fakatonga',\n        english: 'Tongan',\n        flag: '🇹🇴'\n    },\n    fj: {\n        native: 'Na Vosa Vakaviti',\n        english: 'Fijian',\n        flag: '🇫🇯'\n    },\n    sw: {\n        native: 'Kiswahili',\n        english: 'Swahili',\n        flag: '🇰🇪'\n    },\n    am: {\n        native: 'አማርኛ',\n        english: 'Amharic',\n        flag: '🇪🇹'\n    },\n    ti: {\n        native: 'ትግርኛ',\n        english: 'Tigrinya',\n        flag: '🇪🇷'\n    },\n    om: {\n        native: 'Afaan Oromoo',\n        english: 'Oromo',\n        flag: '🇪🇹'\n    },\n    so: {\n        native: 'Soomaali',\n        english: 'Somali',\n        flag: '🇸🇴'\n    },\n    rw: {\n        native: 'Ikinyarwanda',\n        english: 'Kinyarwanda',\n        flag: '🇷🇼'\n    },\n    rn: {\n        native: 'Ikirundi',\n        english: 'Kirundi',\n        flag: '🇧🇮'\n    },\n    lg: {\n        native: 'Luganda',\n        english: 'Luganda',\n        flag: '🇺🇬'\n    },\n    ak: {\n        native: 'Akan',\n        english: 'Akan',\n        flag: '🇬🇭'\n    },\n    tw: {\n        native: 'Twi',\n        english: 'Twi',\n        flag: '🇬🇭'\n    },\n    yo: {\n        native: 'Yorùbá',\n        english: 'Yoruba',\n        flag: '🇳🇬'\n    },\n    ig: {\n        native: 'Igbo',\n        english: 'Igbo',\n        flag: '🇳🇬'\n    },\n    ha: {\n        native: 'Hausa',\n        english: 'Hausa',\n        flag: '🇳🇬'\n    },\n    ff: {\n        native: 'Fulfulde',\n        english: 'Fulah',\n        flag: '🇸🇳'\n    },\n    wo: {\n        native: 'Wolof',\n        english: 'Wolof',\n        flag: '🇸🇳'\n    },\n    sn: {\n        native: 'ChiShona',\n        english: 'Shona',\n        flag: '🇿🇼'\n    },\n    zu: {\n        native: 'IsiZulu',\n        english: 'Zulu',\n        flag: '🇿🇦'\n    },\n    xh: {\n        native: 'IsiXhosa',\n        english: 'Xhosa',\n        flag: '🇿🇦'\n    },\n    af: {\n        native: 'Afrikaans',\n        english: 'Afrikaans',\n        flag: '🇿🇦'\n    },\n    st: {\n        native: 'Sesotho',\n        english: 'Sesotho',\n        flag: '🇱🇸'\n    },\n    tn: {\n        native: 'Setswana',\n        english: 'Setswana',\n        flag: '🇧🇼'\n    },\n    ss: {\n        native: 'SiSwati',\n        english: 'Siswati',\n        flag: '🇸🇿'\n    },\n    ve: {\n        native: 'Tshivenḓa',\n        english: 'Tshivenda',\n        flag: '🇿🇦'\n    },\n    ts: {\n        native: 'Xitsonga',\n        english: 'Xitsonga',\n        flag: '🇿🇦'\n    },\n    nr: {\n        native: 'IsiNdebele',\n        english: 'Ndebele',\n        flag: '🇿🇦'\n    },\n    he: {\n        native: 'עברית',\n        english: 'Hebrew',\n        flag: '🇮🇱'\n    },\n    yi: {\n        native: 'ייִדיש',\n        english: 'Yiddish',\n        flag: '🏳️'\n    },\n    jv: {\n        native: 'Basa Jawa',\n        english: 'Javanese',\n        flag: '🇮🇩'\n    },\n    su: {\n        native: 'Basa Sunda',\n        english: 'Sundanese',\n        flag: '🇮🇩'\n    },\n    mad: {\n        native: 'Basa Madhura',\n        english: 'Madurese',\n        flag: '🇮🇩'\n    },\n    ban: {\n        native: 'Basa Bali',\n        english: 'Balinese',\n        flag: '🇮🇩'\n    },\n    bug: {\n        native: 'Basa Ugi',\n        english: 'Buginese',\n        flag: '🇮🇩'\n    },\n    mak: {\n        native: 'Basa Mangkasara',\n        english: 'Makasar',\n        flag: '🇮🇩'\n    },\n    min: {\n        native: 'Baso Minangkabau',\n        english: 'Minangkabau',\n        flag: '🇮🇩'\n    },\n    ace: {\n        native: 'Bahsa Acèh',\n        english: 'Acehnese',\n        flag: '🇮🇩'\n    },\n    bjn: {\n        native: 'Bahasa Banjar',\n        english: 'Banjar',\n        flag: '🇮🇩'\n    },\n    bbc: {\n        native: 'Hata Batak Toba',\n        english: 'Batak Toba',\n        flag: '🇮🇩'\n    },\n    nij: {\n        native: 'Bahasa Ngaju',\n        english: 'Ngaju',\n        flag: '🇮🇩'\n    },\n    rej: {\n        native: 'Bahasa Rejang',\n        english: 'Rejang',\n        flag: '🇮🇩'\n    },\n    sas: {\n        native: 'Basa Sasak',\n        english: 'Sasak',\n        flag: '🇮🇩'\n    },\n    tet: {\n        native: 'Tetun',\n        english: 'Tetum',\n        flag: '🇹🇱'\n    }\n};\nfunction isValidLocale(locale) {\n    return locales.includes(locale);\n}\nfunction getLocaleDirection(locale) {\n    const rtlLocales = [\n        'ar',\n        'fa',\n        'ur',\n        'he',\n        'yi'\n    ];\n    return rtlLocales.includes(locale) ? 'rtl' : 'ltr';\n}\nfunction getLocaleFontFamily(locale) {\n    const fontFamilies = {\n        ar: 'Noto Sans Arabic, Arial, sans-serif',\n        fa: 'Noto Sans Arabic, Arial, sans-serif',\n        ur: 'Noto Sans Arabic, Arial, sans-serif',\n        he: 'Noto Sans Hebrew, Arial, sans-serif',\n        zh: 'Noto Sans SC, Arial, sans-serif',\n        ja: 'Noto Sans JP, Arial, sans-serif',\n        ko: 'Noto Sans KR, Arial, sans-serif',\n        th: 'Noto Sans Thai, Arial, sans-serif',\n        hi: 'Noto Sans Devanagari, Arial, sans-serif',\n        bn: 'Noto Sans Bengali, Arial, sans-serif',\n        ta: 'Noto Sans Tamil, Arial, sans-serif',\n        te: 'Noto Sans Telugu, Arial, sans-serif',\n        ml: 'Noto Sans Malayalam, Arial, sans-serif',\n        kn: 'Noto Sans Kannada, Arial, sans-serif',\n        gu: 'Noto Sans Gujarati, Arial, sans-serif',\n        pa: 'Noto Sans Gurmukhi, Arial, sans-serif',\n        or: 'Noto Sans Oriya, Arial, sans-serif',\n        as: 'Noto Sans Bengali, Arial, sans-serif',\n        ne: 'Noto Sans Devanagari, Arial, sans-serif',\n        si: 'Noto Sans Sinhala, Arial, sans-serif',\n        my: 'Noto Sans Myanmar, Arial, sans-serif',\n        lo: 'Noto Sans Lao, Arial, sans-serif',\n        km: 'Noto Sans Khmer, Arial, sans-serif',\n        vi: 'Noto Sans Vietnamese, Arial, sans-serif',\n        ka: 'Noto Sans Georgian, Arial, sans-serif',\n        hy: 'Noto Sans Armenian, Arial, sans-serif',\n        am: 'Noto Sans Ethiopic, Arial, sans-serif',\n        ti: 'Noto Sans Ethiopic, Arial, sans-serif'\n    };\n    return fontFamilies[locale] || 'Inter, system-ui, sans-serif';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '').replace(/[\\s_-]+/g, '-').replace(/^-+|-+$/g, '');\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + '...';\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency\n    }).format(amount);\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/locales/ar.ts":
/*!***************************!*\
  !*** ./src/locales/ar.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ar: () => (/* binding */ ar)\n/* harmony export */ });\nconst ar = {\n    nav: {\n        home: 'الرئيسية',\n        about: 'من نحن',\n        services: 'الخدمات',\n        universities: 'الجامعات',\n        programs: 'البرامج',\n        blog: 'المدونة',\n        contact: 'اتصل بنا',\n        applyNow: 'قدم الآن',\n        getStarted: 'ابدأ',\n        language: 'اللغة'\n    },\n    hero: {\n        title: 'بوابتك إلى التعليم العالمي في شمال قبرص',\n        subtitle: 'ادرس في الخارج بثقة',\n        description: 'إرشاد خبير للطلاب الدوليين الساعين للحصول على تعليم عالي الجودة في أفضل الجامعات في شمال قبرص. من التقديم إلى التخرج، نحن معك في كل خطوة.',\n        ctaPrimary: 'ابدأ رحلتك',\n        ctaSecondary: 'استكشف الجامعات',\n        trustBadge: 'موثوق من قبل أكثر من 10,000 طالب حول العالم',\n        studentsServed: 'طالب تم خدمتهم',\n        successRate: 'معدل النجاح',\n        yearsExperience: 'سنوات الخبرة'\n    },\n    about: {\n        title: 'حول مجموعة فورين جيت',\n        subtitle: 'شريكك التعليمي الموثوق',\n        description: 'نحن استشارة تعليمية رائدة متخصصة في مساعدة الطلاب الدوليين على تحقيق أحلامهم الأكاديمية في شمال قبرص.',\n        mission: 'تقديم إرشاد تعليمي شامل وخدمات دعم تمكن الطلاب من النجاح في رحلتهم الأكاديمية الدولية.',\n        vision: 'أن نكون الجسر الأكثر ثقة الذي يربط الطلاب في جميع أنحاء العالم بفرص التعليم الجيد في شمال قبرص.',\n        values: 'التميز والنزاهة والابتكار ونجاح الطلاب',\n        whyChooseUs: 'لماذا تختارنا',\n        experience: 'أكثر من 15 سنة خبرة',\n        expertise: 'إرشاد خبير',\n        support: 'دعم 24/7',\n        success: '98% معدل نجاح'\n    },\n    services: {\n        title: 'خدماتنا',\n        subtitle: 'دعم شامل لرحلتك التعليمية',\n        universitySelection: 'اختيار الجامعة',\n        universitySelectionDesc: 'إرشاد خبير لاختيار الجامعة والبرنامج المناسبين بناءً على أهدافك وتفضيلاتك.',\n        admissionGuidance: 'إرشاد القبول',\n        admissionGuidanceDesc: 'دعم كامل خلال عملية التقديم، من إعداد الوثائق إلى التقديم.',\n        visaSupport: 'دعم التأشيرة',\n        visaSupportDesc: 'مساعدة مهنية في طلبات التأشيرة وإجراءات الهجرة.',\n        accommodationHelp: 'مساعدة السكن',\n        accommodationHelpDesc: 'العثور على خيارات سكن مناسبة بالقرب من جامعتك مع خدمات الإقامة لدينا.',\n        scholarshipAssistance: 'مساعدة المنح الدراسية',\n        scholarshipAssistanceDesc: 'تحديد والتقدم لفرص المنح الدراسية والمساعدة المالية.',\n        ongoingSupport: 'الدعم المستمر',\n        ongoingSupportDesc: 'دعم مستمر طوال دراستك، من الوصول إلى التخرج.'\n    },\n    universities: {\n        title: 'الجامعات الشريكة',\n        subtitle: 'أفضل الجامعات في شمال قبرص',\n        emu: 'جامعة شرق البحر المتوسط',\n        neu: 'جامعة الشرق الأدنى',\n        ciu: 'جامعة قبرص الدولية',\n        programs: 'البرامج',\n        students: 'الطلاب',\n        established: 'تأسست',\n        accreditation: 'الاعتماد',\n        tuitionFrom: 'الرسوم الدراسية من',\n        learnMore: 'اعرف المزيد',\n        applyNow: 'قدم الآن'\n    },\n    programs: {\n        title: 'البرامج الدراسية',\n        subtitle: 'برامج أكاديمية متنوعة تناسب اهتماماتك',\n        engineering: 'الهندسة',\n        medicine: 'الطب',\n        business: 'الأعمال',\n        arts: 'الفنون والعلوم الإنسانية',\n        sciences: 'العلوم',\n        law: 'القانون',\n        architecture: 'العمارة',\n        education: 'التعليم',\n        duration: 'المدة',\n        language: 'اللغة',\n        degree: 'الدرجة',\n        bachelor: 'البكالوريوس',\n        master: 'الماجستير',\n        doctorate: 'الدكتوراه'\n    },\n    testimonials: {\n        title: 'قصص نجاح الطلاب',\n        subtitle: 'استمع من طلابنا الناجحين',\n        readMore: 'اقرأ المزيد',\n        showLess: 'أظهر أقل',\n        verified: 'طالب موثق',\n        graduate: 'خريج',\n        currentStudent: 'طالب حالي'\n    },\n    contact: {\n        title: 'اتصل بنا',\n        subtitle: 'تواصل مع خبراء التعليم لدينا',\n        getInTouch: 'تواصل معنا',\n        name: 'الاسم الكامل',\n        email: 'عنوان البريد الإلكتروني',\n        phone: 'رقم الهاتف',\n        message: 'الرسالة',\n        subject: 'الموضوع',\n        send: 'إرسال الرسالة',\n        sending: 'جاري الإرسال...',\n        sent: 'تم إرسال الرسالة بنجاح!',\n        error: 'فشل في إرسال الرسالة. يرجى المحاولة مرة أخرى.',\n        required: 'هذا الحقل مطلوب',\n        invalidEmail: 'يرجى إدخال عنوان بريد إلكتروني صحيح',\n        office: 'ساعات العمل',\n        hours: 'الاثنين - الجمعة: 9:00 صباحاً - 6:00 مساءً',\n        emergency: 'دعم الطوارئ متاح 24/7'\n    },\n    footer: {\n        description: 'شريكك الموثوق للتعليم الدولي في شمال قبرص. إرشاد خبير من التقديم إلى التخرج.',\n        quickLinks: 'روابط سريعة',\n        services: 'الخدمات',\n        contact: 'معلومات الاتصال',\n        followUs: 'تابعنا',\n        newsletter: 'النشرة الإخبارية',\n        newsletterDesc: 'اشترك للحصول على آخر التحديثات حول الجامعات والبرامج والمنح الدراسية.',\n        subscribe: 'اشترك',\n        subscribing: 'جاري الاشتراك...',\n        subscribed: 'تم الاشتراك بنجاح!',\n        privacy: 'سياسة الخصوصية',\n        terms: 'شروط الخدمة',\n        cookies: 'سياسة ملفات تعريف الارتباط',\n        sitemap: 'خريطة الموقع',\n        allRightsReserved: 'جميع الحقوق محفوظة.'\n    },\n    common: {\n        loading: 'جاري التحميل...',\n        error: 'خطأ',\n        success: 'نجح',\n        warning: 'تحذير',\n        info: 'معلومات',\n        close: 'إغلاق',\n        cancel: 'إلغاء',\n        confirm: 'تأكيد',\n        save: 'حفظ',\n        edit: 'تحرير',\n        delete: 'حذف',\n        search: 'بحث',\n        filter: 'تصفية',\n        sort: 'ترتيب',\n        next: 'التالي',\n        previous: 'السابق',\n        page: 'صفحة',\n        of: 'من',\n        showing: 'عرض',\n        results: 'نتائج',\n        noResults: 'لم يتم العثور على نتائج',\n        tryAgain: 'حاول مرة أخرى',\n        learnMore: 'اعرف المزيد',\n        readMore: 'اقرأ المزيد',\n        showMore: 'أظهر المزيد',\n        showLess: 'أظهر أقل',\n        viewAll: 'عرض الكل',\n        backToTop: 'العودة إلى الأعلى'\n    },\n    chatbot: {\n        title: 'مساعد التعليم',\n        placeholder: 'اسأل عن الجامعات والبرامج والتكاليف...',\n        send: 'إرسال',\n        thinking: 'يفكر...',\n        error: 'عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.',\n        retry: 'إعادة المحاولة',\n        clear: 'مسح المحادثة',\n        minimize: 'تصغير',\n        maximize: 'تكبير',\n        close: 'إغلاق',\n        greeting: 'مرحباً! أنا هنا لمساعدتك في رحلتك التعليمية. ماذا تريد أن تعرف؟',\n        suggestions: 'أسئلة مقترحة',\n        typing: 'يكتب...',\n        offline: 'غير متصل',\n        online: 'متصل'\n    },\n    forms: {\n        firstName: 'الاسم الأول',\n        lastName: 'اسم العائلة',\n        fullName: 'الاسم الكامل',\n        email: 'عنوان البريد الإلكتروني',\n        phone: 'رقم الهاتف',\n        country: 'البلد',\n        city: 'المدينة',\n        address: 'العنوان',\n        zipCode: 'الرمز البريدي',\n        dateOfBirth: 'تاريخ الميلاد',\n        gender: 'الجنس',\n        male: 'ذكر',\n        female: 'أنثى',\n        other: 'آخر',\n        preferNotToSay: 'أفضل عدم القول',\n        nationality: 'الجنسية',\n        passportNumber: 'رقم جواز السفر',\n        education: 'مستوى التعليم',\n        highSchool: 'المدرسة الثانوية',\n        bachelor: 'درجة البكالوريوس',\n        master: 'درجة الماجستير',\n        doctorate: 'الدكتوراه',\n        workExperience: 'خبرة العمل',\n        englishLevel: 'مستوى الإنجليزية',\n        beginner: 'مبتدئ',\n        intermediate: 'متوسط',\n        advanced: 'متقدم',\n        native: 'لغة أم',\n        interestedProgram: 'البرنامج المهتم به',\n        interestedUniversity: 'الجامعة المهتم بها',\n        startDate: 'تاريخ البدء المفضل',\n        additionalInfo: 'معلومات إضافية',\n        agreeTerms: 'أوافق على شروط الخدمة',\n        agreePrivacy: 'أوافق على سياسة الخصوصية',\n        agreeMarketing: 'أوافق على تلقي الاتصالات التسويقية',\n        submit: 'إرسال',\n        submitting: 'جاري الإرسال...',\n        submitted: 'تم الإرسال بنجاح!',\n        required: 'حقل مطلوب',\n        invalid: 'تنسيق غير صحيح',\n        tooShort: 'قصير جداً',\n        tooLong: 'طويل جداً',\n        passwordMismatch: 'كلمات المرور غير متطابقة'\n    },\n    costs: {\n        title: 'التكاليف والمنح الدراسية',\n        subtitle: 'تعليم بأسعار معقولة مع خيارات الدعم المالي',\n        tuitionFees: 'الرسوم الدراسية',\n        livingCosts: 'تكاليف المعيشة',\n        totalCost: 'التكلفة الإجمالية',\n        scholarships: 'المنح الدراسية',\n        financialAid: 'المساعدة المالية',\n        paymentPlans: 'خطط الدفع',\n        currency: 'دولار أمريكي',\n        perYear: 'سنوياً',\n        perMonth: 'شهرياً',\n        accommodation: 'الإقامة',\n        food: 'الطعام والوجبات',\n        transportation: 'النقل',\n        books: 'الكتب واللوازم',\n        personal: 'المصاريف الشخصية',\n        insurance: 'التأمين الصحي',\n        visa: 'التأشيرة والهجرة',\n        other: 'مصاريف أخرى',\n        meritScholarship: 'منحة الجدارة',\n        needBasedAid: 'مساعدة قائمة على الحاجة',\n        earlyBird: 'خصم التسجيل المبكر',\n        siblingDiscount: 'خصم الأشقاء',\n        calculate: 'احسب التكاليف',\n        getQuote: 'احصل على عرض سعر'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/ar.ts\n");

/***/ }),

/***/ "(ssr)/./src/locales/en.ts":
/*!***************************!*\
  !*** ./src/locales/en.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   en: () => (/* binding */ en)\n/* harmony export */ });\nconst en = {\n    nav: {\n        home: 'Home',\n        about: 'About',\n        services: 'Services',\n        universities: 'Universities',\n        programs: 'Programs',\n        blog: 'Blog',\n        contact: 'Contact',\n        applyNow: 'Apply Now',\n        getStarted: 'Get Started',\n        language: 'Language'\n    },\n    hero: {\n        title: 'Your Gateway to World-Class Education in Northern Cyprus',\n        subtitle: 'Study Abroad with Confidence',\n        description: 'Expert guidance for international students seeking quality education at top universities in Northern Cyprus. From application to graduation, we\\'re with you every step of the way.',\n        ctaPrimary: 'Start Your Journey',\n        ctaSecondary: 'Explore Universities',\n        trustBadge: 'Trusted by 10,000+ Students Worldwide',\n        studentsServed: 'Students Served',\n        successRate: 'Success Rate',\n        yearsExperience: 'Years Experience'\n    },\n    about: {\n        title: 'About Foreingate Group',\n        subtitle: 'Your Trusted Education Partner',\n        description: 'We are a leading educational consultancy specializing in helping international students achieve their academic dreams in Northern Cyprus.',\n        mission: 'To provide comprehensive educational guidance and support services that empower students to succeed in their international academic journey.',\n        vision: 'To be the most trusted bridge connecting students worldwide with quality education opportunities in Northern Cyprus.',\n        values: 'Excellence, Integrity, Innovation, and Student Success',\n        whyChooseUs: 'Why Choose Us',\n        experience: '15+ Years Experience',\n        expertise: 'Expert Guidance',\n        support: '24/7 Support',\n        success: '98% Success Rate'\n    },\n    services: {\n        title: 'Our Services',\n        subtitle: 'Comprehensive Support for Your Educational Journey',\n        universitySelection: 'University Selection',\n        universitySelectionDesc: 'Expert guidance to choose the right university and program based on your goals and preferences.',\n        admissionGuidance: 'Admission Guidance',\n        admissionGuidanceDesc: 'Complete support through the application process, from document preparation to submission.',\n        visaSupport: 'Visa Support',\n        visaSupportDesc: 'Professional assistance with visa applications and immigration procedures.',\n        accommodationHelp: 'Accommodation Help',\n        accommodationHelpDesc: 'Find suitable housing options near your university with our accommodation services.',\n        scholarshipAssistance: 'Scholarship Assistance',\n        scholarshipAssistanceDesc: 'Identify and apply for scholarships and financial aid opportunities.',\n        ongoingSupport: 'Ongoing Support',\n        ongoingSupportDesc: 'Continuous support throughout your studies, from arrival to graduation.'\n    },\n    universities: {\n        title: 'Partner Universities',\n        subtitle: 'Top-Ranked Universities in Northern Cyprus',\n        emu: 'Eastern Mediterranean University',\n        neu: 'Near East University',\n        ciu: 'Cyprus International University',\n        programs: 'Programs',\n        students: 'Students',\n        established: 'Established',\n        accreditation: 'Accreditation',\n        tuitionFrom: 'Tuition from',\n        learnMore: 'Learn More',\n        applyNow: 'Apply Now'\n    },\n    programs: {\n        title: 'Study Programs',\n        subtitle: 'Diverse Academic Programs to Match Your Interests',\n        engineering: 'Engineering',\n        medicine: 'Medicine',\n        business: 'Business',\n        arts: 'Arts & Humanities',\n        sciences: 'Sciences',\n        law: 'Law',\n        architecture: 'Architecture',\n        education: 'Education',\n        duration: 'Duration',\n        language: 'Language',\n        degree: 'Degree',\n        bachelor: 'Bachelor\\'s',\n        master: 'Master\\'s',\n        doctorate: 'Doctorate'\n    },\n    testimonials: {\n        title: 'Student Success Stories',\n        subtitle: 'Hear from Our Successful Students',\n        readMore: 'Read More',\n        showLess: 'Show Less',\n        verified: 'Verified Student',\n        graduate: 'Graduate',\n        currentStudent: 'Current Student'\n    },\n    contact: {\n        title: 'Contact Us',\n        subtitle: 'Get in Touch with Our Education Experts',\n        getInTouch: 'Get in Touch',\n        name: 'Full Name',\n        email: 'Email Address',\n        phone: 'Phone Number',\n        message: 'Message',\n        subject: 'Subject',\n        send: 'Send Message',\n        sending: 'Sending...',\n        sent: 'Message Sent Successfully!',\n        error: 'Failed to send message. Please try again.',\n        required: 'This field is required',\n        invalidEmail: 'Please enter a valid email address',\n        office: 'Office Hours',\n        hours: 'Monday - Friday: 9:00 AM - 6:00 PM',\n        emergency: '24/7 Emergency Support Available'\n    },\n    footer: {\n        description: 'Your trusted partner for international education in Northern Cyprus. Expert guidance from application to graduation.',\n        quickLinks: 'Quick Links',\n        services: 'Services',\n        contact: 'Contact Info',\n        followUs: 'Follow Us',\n        newsletter: 'Newsletter',\n        newsletterDesc: 'Subscribe to get the latest updates on universities, programs, and scholarships.',\n        subscribe: 'Subscribe',\n        subscribing: 'Subscribing...',\n        subscribed: 'Subscribed Successfully!',\n        privacy: 'Privacy Policy',\n        terms: 'Terms of Service',\n        cookies: 'Cookie Policy',\n        sitemap: 'Sitemap',\n        allRightsReserved: 'All rights reserved.'\n    },\n    common: {\n        loading: 'Loading...',\n        error: 'Error',\n        success: 'Success',\n        warning: 'Warning',\n        info: 'Information',\n        close: 'Close',\n        cancel: 'Cancel',\n        confirm: 'Confirm',\n        save: 'Save',\n        edit: 'Edit',\n        delete: 'Delete',\n        search: 'Search',\n        filter: 'Filter',\n        sort: 'Sort',\n        next: 'Next',\n        previous: 'Previous',\n        page: 'Page',\n        of: 'of',\n        showing: 'Showing',\n        results: 'results',\n        noResults: 'No results found',\n        tryAgain: 'Try Again',\n        learnMore: 'Learn More',\n        readMore: 'Read More',\n        showMore: 'Show More',\n        showLess: 'Show Less',\n        viewAll: 'View All',\n        backToTop: 'Back to Top'\n    },\n    chatbot: {\n        title: 'Education Assistant',\n        placeholder: 'Ask me about universities, programs, costs...',\n        send: 'Send',\n        thinking: 'Thinking...',\n        error: 'Sorry, I encountered an error. Please try again.',\n        retry: 'Retry',\n        clear: 'Clear Chat',\n        minimize: 'Minimize',\n        maximize: 'Maximize',\n        close: 'Close',\n        greeting: 'Hello! I\\'m here to help you with your education journey. What would you like to know?',\n        suggestions: 'Suggested Questions',\n        typing: 'Typing...',\n        offline: 'Offline',\n        online: 'Online'\n    },\n    forms: {\n        firstName: 'First Name',\n        lastName: 'Last Name',\n        fullName: 'Full Name',\n        email: 'Email Address',\n        phone: 'Phone Number',\n        country: 'Country',\n        city: 'City',\n        address: 'Address',\n        zipCode: 'ZIP Code',\n        dateOfBirth: 'Date of Birth',\n        gender: 'Gender',\n        male: 'Male',\n        female: 'Female',\n        other: 'Other',\n        preferNotToSay: 'Prefer not to say',\n        nationality: 'Nationality',\n        passportNumber: 'Passport Number',\n        education: 'Education Level',\n        highSchool: 'High School',\n        bachelor: 'Bachelor\\'s Degree',\n        master: 'Master\\'s Degree',\n        doctorate: 'Doctorate',\n        workExperience: 'Work Experience',\n        englishLevel: 'English Level',\n        beginner: 'Beginner',\n        intermediate: 'Intermediate',\n        advanced: 'Advanced',\n        native: 'Native',\n        interestedProgram: 'Interested Program',\n        interestedUniversity: 'Interested University',\n        startDate: 'Preferred Start Date',\n        additionalInfo: 'Additional Information',\n        agreeTerms: 'I agree to the Terms of Service',\n        agreePrivacy: 'I agree to the Privacy Policy',\n        agreeMarketing: 'I agree to receive marketing communications',\n        submit: 'Submit',\n        submitting: 'Submitting...',\n        submitted: 'Submitted Successfully!',\n        required: 'Required field',\n        invalid: 'Invalid format',\n        tooShort: 'Too short',\n        tooLong: 'Too long',\n        passwordMismatch: 'Passwords do not match'\n    },\n    costs: {\n        title: 'Costs & Scholarships',\n        subtitle: 'Affordable Education with Financial Support Options',\n        tuitionFees: 'Tuition Fees',\n        livingCosts: 'Living Costs',\n        totalCost: 'Total Cost',\n        scholarships: 'Scholarships',\n        financialAid: 'Financial Aid',\n        paymentPlans: 'Payment Plans',\n        currency: 'USD',\n        perYear: 'per year',\n        perMonth: 'per month',\n        accommodation: 'Accommodation',\n        food: 'Food & Meals',\n        transportation: 'Transportation',\n        books: 'Books & Supplies',\n        personal: 'Personal Expenses',\n        insurance: 'Health Insurance',\n        visa: 'Visa & Immigration',\n        other: 'Other Expenses',\n        meritScholarship: 'Merit Scholarship',\n        needBasedAid: 'Need-Based Aid',\n        earlyBird: 'Early Bird Discount',\n        siblingDiscount: 'Sibling Discount',\n        calculate: 'Calculate Costs',\n        getQuote: 'Get Quote'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/en.ts\n");

/***/ }),

/***/ "(ssr)/./src/locales/es.ts":
/*!***************************!*\
  !*** ./src/locales/es.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   es: () => (/* binding */ es)\n/* harmony export */ });\nconst es = {\n    nav: {\n        home: 'Inicio',\n        about: 'Acerca de',\n        services: 'Servicios',\n        universities: 'Universidades',\n        programs: 'Programas',\n        blog: 'Blog',\n        contact: 'Contacto',\n        applyNow: 'Aplicar Ahora',\n        getStarted: 'Comenzar',\n        language: 'Idioma'\n    },\n    hero: {\n        title: 'Tu Puerta de Entrada a la Educación de Clase Mundial en Chipre del Norte',\n        subtitle: 'Estudia en el Extranjero con Confianza',\n        description: 'Orientación experta para estudiantes internacionales que buscan educación de calidad en las mejores universidades de Chipre del Norte. Desde la solicitud hasta la graduación, estamos contigo en cada paso.',\n        ctaPrimary: 'Comienza tu Viaje',\n        ctaSecondary: 'Explorar Universidades',\n        trustBadge: 'Confiado por más de 10,000 estudiantes en todo el mundo',\n        studentsServed: 'Estudiantes Atendidos',\n        successRate: 'Tasa de Éxito',\n        yearsExperience: 'Años de Experiencia'\n    },\n    about: {\n        title: 'Acerca del Grupo Foreingate',\n        subtitle: 'Tu Socio Educativo de Confianza',\n        description: 'Somos una consultoría educativa líder especializada en ayudar a estudiantes internacionales a lograr sus sueños académicos en Chipre del Norte.',\n        mission: 'Proporcionar orientación educativa integral y servicios de apoyo que permitan a los estudiantes tener éxito en su viaje académico internacional.',\n        vision: 'Ser el puente más confiable que conecte a estudiantes de todo el mundo con oportunidades de educación de calidad en Chipre del Norte.',\n        values: 'Excelencia, Integridad, Innovación y Éxito Estudiantil',\n        whyChooseUs: 'Por Qué Elegirnos',\n        experience: '15+ Años de Experiencia',\n        expertise: 'Orientación Experta',\n        support: 'Soporte 24/7',\n        success: '98% Tasa de Éxito'\n    },\n    services: {\n        title: 'Nuestros Servicios',\n        subtitle: 'Apoyo Integral para tu Viaje Educativo',\n        universitySelection: 'Selección de Universidad',\n        universitySelectionDesc: 'Orientación experta para elegir la universidad y programa correctos basados en tus objetivos y preferencias.',\n        admissionGuidance: 'Orientación de Admisión',\n        admissionGuidanceDesc: 'Apoyo completo durante el proceso de solicitud, desde la preparación de documentos hasta la presentación.',\n        visaSupport: 'Apoyo de Visa',\n        visaSupportDesc: 'Asistencia profesional con solicitudes de visa y procedimientos de inmigración.',\n        accommodationHelp: 'Ayuda de Alojamiento',\n        accommodationHelpDesc: 'Encuentra opciones de vivienda adecuadas cerca de tu universidad con nuestros servicios de alojamiento.',\n        scholarshipAssistance: 'Asistencia de Becas',\n        scholarshipAssistanceDesc: 'Identifica y solicita becas y oportunidades de ayuda financiera.',\n        ongoingSupport: 'Apoyo Continuo',\n        ongoingSupportDesc: 'Apoyo continuo durante tus estudios, desde la llegada hasta la graduación.'\n    },\n    universities: {\n        title: 'Universidades Asociadas',\n        subtitle: 'Universidades de Alto Rango en Chipre del Norte',\n        emu: 'Universidad del Mediterráneo Oriental',\n        neu: 'Universidad del Cercano Oriente',\n        ciu: 'Universidad Internacional de Chipre',\n        programs: 'Programas',\n        students: 'Estudiantes',\n        established: 'Establecida',\n        accreditation: 'Acreditación',\n        tuitionFrom: 'Matrícula desde',\n        learnMore: 'Saber Más',\n        applyNow: 'Aplicar Ahora'\n    },\n    programs: {\n        title: 'Programas de Estudio',\n        subtitle: 'Programas Académicos Diversos para Coincidir con tus Intereses',\n        engineering: 'Ingeniería',\n        medicine: 'Medicina',\n        business: 'Negocios',\n        arts: 'Artes y Humanidades',\n        sciences: 'Ciencias',\n        law: 'Derecho',\n        architecture: 'Arquitectura',\n        education: 'Educación',\n        duration: 'Duración',\n        language: 'Idioma',\n        degree: 'Título',\n        bachelor: 'Licenciatura',\n        master: 'Maestría',\n        doctorate: 'Doctorado'\n    },\n    testimonials: {\n        title: 'Historias de Éxito de Estudiantes',\n        subtitle: 'Escucha de Nuestros Estudiantes Exitosos',\n        readMore: 'Leer Más',\n        showLess: 'Mostrar Menos',\n        verified: 'Estudiante Verificado',\n        graduate: 'Graduado',\n        currentStudent: 'Estudiante Actual'\n    },\n    contact: {\n        title: 'Contáctanos',\n        subtitle: 'Ponte en Contacto con Nuestros Expertos en Educación',\n        getInTouch: 'Ponerse en Contacto',\n        name: 'Nombre Completo',\n        email: 'Dirección de Correo Electrónico',\n        phone: 'Número de Teléfono',\n        message: 'Mensaje',\n        subject: 'Asunto',\n        send: 'Enviar Mensaje',\n        sending: 'Enviando...',\n        sent: '¡Mensaje Enviado Exitosamente!',\n        error: 'Error al enviar el mensaje. Por favor, inténtalo de nuevo.',\n        required: 'Este campo es requerido',\n        invalidEmail: 'Por favor, ingresa una dirección de correo electrónico válida',\n        office: 'Horario de Oficina',\n        hours: 'Lunes - Viernes: 9:00 AM - 6:00 PM',\n        emergency: 'Soporte de Emergencia 24/7 Disponible'\n    },\n    footer: {\n        description: 'Tu socio de confianza para la educación internacional en Chipre del Norte. Orientación experta desde la solicitud hasta la graduación.',\n        quickLinks: 'Enlaces Rápidos',\n        services: 'Servicios',\n        contact: 'Información de Contacto',\n        followUs: 'Síguenos',\n        newsletter: 'Boletín',\n        newsletterDesc: 'Suscríbete para recibir las últimas actualizaciones sobre universidades, programas y becas.',\n        subscribe: 'Suscribirse',\n        subscribing: 'Suscribiendo...',\n        subscribed: '¡Suscrito Exitosamente!',\n        privacy: 'Política de Privacidad',\n        terms: 'Términos de Servicio',\n        cookies: 'Política de Cookies',\n        sitemap: 'Mapa del Sitio',\n        allRightsReserved: 'Todos los derechos reservados.'\n    },\n    common: {\n        loading: 'Cargando...',\n        error: 'Error',\n        success: 'Éxito',\n        warning: 'Advertencia',\n        info: 'Información',\n        close: 'Cerrar',\n        cancel: 'Cancelar',\n        confirm: 'Confirmar',\n        save: 'Guardar',\n        edit: 'Editar',\n        delete: 'Eliminar',\n        search: 'Buscar',\n        filter: 'Filtrar',\n        sort: 'Ordenar',\n        next: 'Siguiente',\n        previous: 'Anterior',\n        page: 'Página',\n        of: 'de',\n        showing: 'Mostrando',\n        results: 'resultados',\n        noResults: 'No se encontraron resultados',\n        tryAgain: 'Intentar de Nuevo',\n        learnMore: 'Saber Más',\n        readMore: 'Leer Más',\n        showMore: 'Mostrar Más',\n        showLess: 'Mostrar Menos',\n        viewAll: 'Ver Todo',\n        backToTop: 'Volver Arriba'\n    },\n    chatbot: {\n        title: 'Asistente Educativo',\n        placeholder: 'Pregúntame sobre universidades, programas, costos...',\n        send: 'Enviar',\n        thinking: 'Pensando...',\n        error: 'Lo siento, encontré un error. Por favor, inténtalo de nuevo.',\n        retry: 'Reintentar',\n        clear: 'Limpiar Chat',\n        minimize: 'Minimizar',\n        maximize: 'Maximizar',\n        close: 'Cerrar',\n        greeting: '¡Hola! Estoy aquí para ayudarte en tu viaje educativo. ¿Qué te gustaría saber?',\n        suggestions: 'Preguntas Sugeridas',\n        typing: 'Escribiendo...',\n        offline: 'Desconectado',\n        online: 'En línea'\n    },\n    forms: {\n        firstName: 'Nombre',\n        lastName: 'Apellido',\n        fullName: 'Nombre Completo',\n        email: 'Dirección de Correo Electrónico',\n        phone: 'Número de Teléfono',\n        country: 'País',\n        city: 'Ciudad',\n        address: 'Dirección',\n        zipCode: 'Código Postal',\n        dateOfBirth: 'Fecha de Nacimiento',\n        gender: 'Género',\n        male: 'Masculino',\n        female: 'Femenino',\n        other: 'Otro',\n        preferNotToSay: 'Prefiero no decir',\n        nationality: 'Nacionalidad',\n        passportNumber: 'Número de Pasaporte',\n        education: 'Nivel de Educación',\n        highSchool: 'Escuela Secundaria',\n        bachelor: 'Licenciatura',\n        master: 'Maestría',\n        doctorate: 'Doctorado',\n        workExperience: 'Experiencia Laboral',\n        englishLevel: 'Nivel de Inglés',\n        beginner: 'Principiante',\n        intermediate: 'Intermedio',\n        advanced: 'Avanzado',\n        native: 'Nativo',\n        interestedProgram: 'Programa de Interés',\n        interestedUniversity: 'Universidad de Interés',\n        startDate: 'Fecha de Inicio Preferida',\n        additionalInfo: 'Información Adicional',\n        agreeTerms: 'Acepto los Términos de Servicio',\n        agreePrivacy: 'Acepto la Política de Privacidad',\n        agreeMarketing: 'Acepto recibir comunicaciones de marketing',\n        submit: 'Enviar',\n        submitting: 'Enviando...',\n        submitted: '¡Enviado Exitosamente!',\n        required: 'Campo requerido',\n        invalid: 'Formato inválido',\n        tooShort: 'Muy corto',\n        tooLong: 'Muy largo',\n        passwordMismatch: 'Las contraseñas no coinciden'\n    },\n    costs: {\n        title: 'Costos y Becas',\n        subtitle: 'Educación Asequible con Opciones de Apoyo Financiero',\n        tuitionFees: 'Tasas de Matrícula',\n        livingCosts: 'Costos de Vida',\n        totalCost: 'Costo Total',\n        scholarships: 'Becas',\n        financialAid: 'Ayuda Financiera',\n        paymentPlans: 'Planes de Pago',\n        currency: 'USD',\n        perYear: 'por año',\n        perMonth: 'por mes',\n        accommodation: 'Alojamiento',\n        food: 'Comida y Comidas',\n        transportation: 'Transporte',\n        books: 'Libros y Suministros',\n        personal: 'Gastos Personales',\n        insurance: 'Seguro de Salud',\n        visa: 'Visa e Inmigración',\n        other: 'Otros Gastos',\n        meritScholarship: 'Beca por Mérito',\n        needBasedAid: 'Ayuda Basada en Necesidad',\n        earlyBird: 'Descuento por Inscripción Temprana',\n        siblingDiscount: 'Descuento por Hermanos',\n        calculate: 'Calcular Costos',\n        getQuote: 'Obtener Cotización'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/es.ts\n");

/***/ }),

/***/ "(ssr)/./src/locales/fr.ts":
/*!***************************!*\
  !*** ./src/locales/fr.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fr: () => (/* binding */ fr)\n/* harmony export */ });\nconst fr = {\n    nav: {\n        home: 'Accueil',\n        about: 'À propos',\n        services: 'Services',\n        universities: 'Universités',\n        programs: 'Programmes',\n        blog: 'Blog',\n        contact: 'Contact',\n        applyNow: 'Postuler',\n        getStarted: 'Commencer',\n        language: 'Langue'\n    },\n    hero: {\n        title: 'Votre Porte d\\'Entrée vers l\\'Éducation de Classe Mondiale à Chypre du Nord',\n        subtitle: 'Étudiez à l\\'Étranger en Toute Confiance',\n        description: 'Conseils d\\'experts pour les étudiants internationaux recherchant une éducation de qualité dans les meilleures universités de Chypre du Nord. De la candidature à la remise des diplômes, nous sommes avec vous à chaque étape.',\n        ctaPrimary: 'Commencez Votre Voyage',\n        ctaSecondary: 'Explorer les Universités',\n        trustBadge: 'Approuvé par plus de 10 000 étudiants dans le monde',\n        studentsServed: 'Étudiants Servis',\n        successRate: 'Taux de Réussite',\n        yearsExperience: 'Années d\\'Expérience'\n    },\n    about: {\n        title: 'À propos du Groupe Foreingate',\n        subtitle: 'Votre Partenaire Éducatif de Confiance',\n        description: 'Nous sommes un cabinet de conseil en éducation de premier plan spécialisé dans l\\'aide aux étudiants internationaux pour réaliser leurs rêves académiques à Chypre du Nord.',\n        mission: 'Fournir des conseils éducatifs complets et des services de soutien qui permettent aux étudiants de réussir dans leur parcours académique international.',\n        vision: 'Être le pont le plus fiable reliant les étudiants du monde entier aux opportunités d\\'éducation de qualité à Chypre du Nord.',\n        values: 'Excellence, Intégrité, Innovation et Réussite Étudiante',\n        whyChooseUs: 'Pourquoi Nous Choisir',\n        experience: '15+ Années d\\'Expérience',\n        expertise: 'Conseils d\\'Experts',\n        support: 'Support 24/7',\n        success: '98% Taux de Réussite'\n    },\n    services: {\n        title: 'Nos Services',\n        subtitle: 'Support Complet pour Votre Parcours Éducatif',\n        universitySelection: 'Sélection d\\'Université',\n        universitySelectionDesc: 'Conseils d\\'experts pour choisir la bonne université et le bon programme en fonction de vos objectifs et préférences.',\n        admissionGuidance: 'Conseils d\\'Admission',\n        admissionGuidanceDesc: 'Support complet tout au long du processus de candidature, de la préparation des documents à la soumission.',\n        visaSupport: 'Support Visa',\n        visaSupportDesc: 'Assistance professionnelle pour les demandes de visa et les procédures d\\'immigration.',\n        accommodationHelp: 'Aide au Logement',\n        accommodationHelpDesc: 'Trouvez des options de logement appropriées près de votre université avec nos services d\\'hébergement.',\n        scholarshipAssistance: 'Aide aux Bourses',\n        scholarshipAssistanceDesc: 'Identifiez et postulez pour des bourses et des opportunités d\\'aide financière.',\n        ongoingSupport: 'Support Continu',\n        ongoingSupportDesc: 'Support continu tout au long de vos études, de l\\'arrivée à la remise des diplômes.'\n    },\n    universities: {\n        title: 'Universités Partenaires',\n        subtitle: 'Universités de Premier Rang à Chypre du Nord',\n        emu: 'Université de Méditerranée Orientale',\n        neu: 'Université du Proche-Orient',\n        ciu: 'Université Internationale de Chypre',\n        programs: 'Programmes',\n        students: 'Étudiants',\n        established: 'Établie',\n        accreditation: 'Accréditation',\n        tuitionFrom: 'Frais de scolarité à partir de',\n        learnMore: 'En Savoir Plus',\n        applyNow: 'Postuler'\n    },\n    programs: {\n        title: 'Programmes d\\'Études',\n        subtitle: 'Programmes Académiques Diversifiés pour Correspondre à Vos Intérêts',\n        engineering: 'Ingénierie',\n        medicine: 'Médecine',\n        business: 'Commerce',\n        arts: 'Arts et Sciences Humaines',\n        sciences: 'Sciences',\n        law: 'Droit',\n        architecture: 'Architecture',\n        education: 'Éducation',\n        duration: 'Durée',\n        language: 'Langue',\n        degree: 'Diplôme',\n        bachelor: 'Licence',\n        master: 'Master',\n        doctorate: 'Doctorat'\n    },\n    testimonials: {\n        title: 'Histoires de Réussite d\\'Étudiants',\n        subtitle: 'Écoutez Nos Étudiants Réussis',\n        readMore: 'Lire Plus',\n        showLess: 'Afficher Moins',\n        verified: 'Étudiant Vérifié',\n        graduate: 'Diplômé',\n        currentStudent: 'Étudiant Actuel'\n    },\n    contact: {\n        title: 'Contactez-Nous',\n        subtitle: 'Entrez en Contact avec Nos Experts en Éducation',\n        getInTouch: 'Entrer en Contact',\n        name: 'Nom Complet',\n        email: 'Adresse E-mail',\n        phone: 'Numéro de Téléphone',\n        message: 'Message',\n        subject: 'Sujet',\n        send: 'Envoyer le Message',\n        sending: 'Envoi en cours...',\n        sent: 'Message Envoyé avec Succès !',\n        error: 'Échec de l\\'envoi du message. Veuillez réessayer.',\n        required: 'Ce champ est requis',\n        invalidEmail: 'Veuillez entrer une adresse e-mail valide',\n        office: 'Heures de Bureau',\n        hours: 'Lundi - Vendredi : 9h00 - 18h00',\n        emergency: 'Support d\\'Urgence 24/7 Disponible'\n    },\n    footer: {\n        description: 'Votre partenaire de confiance pour l\\'éducation internationale à Chypre du Nord. Conseils d\\'experts de la candidature à la remise des diplômes.',\n        quickLinks: 'Liens Rapides',\n        services: 'Services',\n        contact: 'Informations de Contact',\n        followUs: 'Suivez-Nous',\n        newsletter: 'Newsletter',\n        newsletterDesc: 'Abonnez-vous pour recevoir les dernières mises à jour sur les universités, programmes et bourses.',\n        subscribe: 'S\\'abonner',\n        subscribing: 'Abonnement en cours...',\n        subscribed: 'Abonné avec Succès !',\n        privacy: 'Politique de Confidentialité',\n        terms: 'Conditions de Service',\n        cookies: 'Politique des Cookies',\n        sitemap: 'Plan du Site',\n        allRightsReserved: 'Tous droits réservés.'\n    },\n    common: {\n        loading: 'Chargement...',\n        error: 'Erreur',\n        success: 'Succès',\n        warning: 'Avertissement',\n        info: 'Information',\n        close: 'Fermer',\n        cancel: 'Annuler',\n        confirm: 'Confirmer',\n        save: 'Sauvegarder',\n        edit: 'Modifier',\n        delete: 'Supprimer',\n        search: 'Rechercher',\n        filter: 'Filtrer',\n        sort: 'Trier',\n        next: 'Suivant',\n        previous: 'Précédent',\n        page: 'Page',\n        of: 'de',\n        showing: 'Affichage',\n        results: 'résultats',\n        noResults: 'Aucun résultat trouvé',\n        tryAgain: 'Réessayer',\n        learnMore: 'En Savoir Plus',\n        readMore: 'Lire Plus',\n        showMore: 'Afficher Plus',\n        showLess: 'Afficher Moins',\n        viewAll: 'Voir Tout',\n        backToTop: 'Retour en Haut'\n    },\n    chatbot: {\n        title: 'Assistant Éducatif',\n        placeholder: 'Demandez-moi à propos des universités, programmes, coûts...',\n        send: 'Envoyer',\n        thinking: 'Réflexion...',\n        error: 'Désolé, j\\'ai rencontré une erreur. Veuillez réessayer.',\n        retry: 'Réessayer',\n        clear: 'Effacer la Discussion',\n        minimize: 'Réduire',\n        maximize: 'Agrandir',\n        close: 'Fermer',\n        greeting: 'Bonjour ! Je suis ici pour vous aider dans votre parcours éducatif. Que souhaitez-vous savoir ?',\n        suggestions: 'Questions Suggérées',\n        typing: 'Frappe...',\n        offline: 'Hors ligne',\n        online: 'En ligne'\n    },\n    forms: {\n        firstName: 'Prénom',\n        lastName: 'Nom de Famille',\n        fullName: 'Nom Complet',\n        email: 'Adresse E-mail',\n        phone: 'Numéro de Téléphone',\n        country: 'Pays',\n        city: 'Ville',\n        address: 'Adresse',\n        zipCode: 'Code Postal',\n        dateOfBirth: 'Date de Naissance',\n        gender: 'Genre',\n        male: 'Homme',\n        female: 'Femme',\n        other: 'Autre',\n        preferNotToSay: 'Préfère ne pas dire',\n        nationality: 'Nationalité',\n        passportNumber: 'Numéro de Passeport',\n        education: 'Niveau d\\'Éducation',\n        highSchool: 'Lycée',\n        bachelor: 'Licence',\n        master: 'Master',\n        doctorate: 'Doctorat',\n        workExperience: 'Expérience Professionnelle',\n        englishLevel: 'Niveau d\\'Anglais',\n        beginner: 'Débutant',\n        intermediate: 'Intermédiaire',\n        advanced: 'Avancé',\n        native: 'Langue Maternelle',\n        interestedProgram: 'Programme d\\'Intérêt',\n        interestedUniversity: 'Université d\\'Intérêt',\n        startDate: 'Date de Début Préférée',\n        additionalInfo: 'Informations Supplémentaires',\n        agreeTerms: 'J\\'accepte les Conditions de Service',\n        agreePrivacy: 'J\\'accepte la Politique de Confidentialité',\n        agreeMarketing: 'J\\'accepte de recevoir des communications marketing',\n        submit: 'Soumettre',\n        submitting: 'Soumission...',\n        submitted: 'Soumis avec Succès !',\n        required: 'Champ requis',\n        invalid: 'Format invalide',\n        tooShort: 'Trop court',\n        tooLong: 'Trop long',\n        passwordMismatch: 'Les mots de passe ne correspondent pas'\n    },\n    costs: {\n        title: 'Coûts et Bourses',\n        subtitle: 'Éducation Abordable avec Options de Soutien Financier',\n        tuitionFees: 'Frais de Scolarité',\n        livingCosts: 'Coûts de la Vie',\n        totalCost: 'Coût Total',\n        scholarships: 'Bourses',\n        financialAid: 'Aide Financière',\n        paymentPlans: 'Plans de Paiement',\n        currency: 'USD',\n        perYear: 'par an',\n        perMonth: 'par mois',\n        accommodation: 'Hébergement',\n        food: 'Nourriture et Repas',\n        transportation: 'Transport',\n        books: 'Livres et Fournitures',\n        personal: 'Dépenses Personnelles',\n        insurance: 'Assurance Santé',\n        visa: 'Visa et Immigration',\n        other: 'Autres Dépenses',\n        meritScholarship: 'Bourse au Mérite',\n        needBasedAid: 'Aide Basée sur les Besoins',\n        earlyBird: 'Remise Inscription Précoce',\n        siblingDiscount: 'Remise Fratrie',\n        calculate: 'Calculer les Coûts',\n        getQuote: 'Obtenir un Devis'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/fr.ts\n");

/***/ }),

/***/ "(ssr)/./src/locales/tr.ts":
/*!***************************!*\
  !*** ./src/locales/tr.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tr: () => (/* binding */ tr)\n/* harmony export */ });\nconst tr = {\n    nav: {\n        home: 'Ana Sayfa',\n        about: 'Hakkımızda',\n        services: 'Hizmetler',\n        universities: 'Üniversiteler',\n        programs: 'Programlar',\n        blog: 'Blog',\n        contact: 'İletişim',\n        applyNow: 'Başvur',\n        getStarted: 'Başla',\n        language: 'Dil'\n    },\n    hero: {\n        title: 'Kuzey Kıbrıs\\'ta Dünya Standartlarında Eğitime Açılan Kapınız',\n        subtitle: 'Güvenle Yurtdışında Eğitim',\n        description: 'Kuzey Kıbrıs\\'taki en iyi üniversitelerde kaliteli eğitim arayan uluslararası öğrenciler için uzman rehberlik. Başvurudan mezuniyete kadar her adımda yanınızdayız.',\n        ctaPrimary: 'Yolculuğuna Başla',\n        ctaSecondary: 'Üniversiteleri Keşfet',\n        trustBadge: 'Dünya Çapında 10.000+ Öğrenci Tarafından Güvenilir',\n        studentsServed: 'Hizmet Verilen Öğrenci',\n        successRate: 'Başarı Oranı',\n        yearsExperience: 'Yıl Deneyim'\n    },\n    about: {\n        title: 'Foreingate Group Hakkında',\n        subtitle: 'Güvenilir Eğitim Ortağınız',\n        description: 'Uluslararası öğrencilerin Kuzey Kıbrıs\\'taki akademik hayallerini gerçekleştirmelerine yardımcı olan önde gelen eğitim danışmanlığıyız.',\n        mission: 'Öğrencilerin uluslararası akademik yolculuklarında başarılı olmalarını sağlayan kapsamlı eğitim rehberliği ve destek hizmetleri sunmak.',\n        vision: 'Dünya çapındaki öğrencileri Kuzey Kıbrıs\\'taki kaliteli eğitim fırsatlarıyla buluşturan en güvenilir köprü olmak.',\n        values: 'Mükemmellik, Dürüstlük, İnovasyon ve Öğrenci Başarısı',\n        whyChooseUs: 'Neden Bizi Seçmelisiniz',\n        experience: '15+ Yıl Deneyim',\n        expertise: 'Uzman Rehberlik',\n        support: '7/24 Destek',\n        success: '%98 Başarı Oranı'\n    },\n    services: {\n        title: 'Hizmetlerimiz',\n        subtitle: 'Eğitim Yolculuğunuz İçin Kapsamlı Destek',\n        universitySelection: 'Üniversite Seçimi',\n        universitySelectionDesc: 'Hedefleriniz ve tercihlerinize göre doğru üniversite ve programı seçmek için uzman rehberlik.',\n        admissionGuidance: 'Kabul Rehberliği',\n        admissionGuidanceDesc: 'Belge hazırlığından başvuru sürecine kadar tam destek.',\n        visaSupport: 'Vize Desteği',\n        visaSupportDesc: 'Vize başvuruları ve göçmenlik prosedürleri için profesyonel yardım.',\n        accommodationHelp: 'Konaklama Yardımı',\n        accommodationHelpDesc: 'Konaklama hizmetlerimizle üniversitenizin yakınında uygun barınma seçenekleri bulun.',\n        scholarshipAssistance: 'Burs Yardımı',\n        scholarshipAssistanceDesc: 'Burs ve mali yardım fırsatlarını belirleyin ve başvurun.',\n        ongoingSupport: 'Sürekli Destek',\n        ongoingSupportDesc: 'Gelişinizden mezuniyetinize kadar eğitiminiz boyunca sürekli destek.'\n    },\n    universities: {\n        title: 'Partner Üniversiteler',\n        subtitle: 'Kuzey Kıbrıs\\'ın En İyi Üniversiteleri',\n        emu: 'Doğu Akdeniz Üniversitesi',\n        neu: 'Yakın Doğu Üniversitesi',\n        ciu: 'Kıbrıs Uluslararası Üniversitesi',\n        programs: 'Program',\n        students: 'Öğrenci',\n        established: 'Kuruluş',\n        accreditation: 'Akreditasyon',\n        tuitionFrom: 'Öğrenim ücreti',\n        learnMore: 'Daha Fazla Bilgi',\n        applyNow: 'Başvur'\n    },\n    programs: {\n        title: 'Eğitim Programları',\n        subtitle: 'İlgi Alanlarınıza Uygun Çeşitli Akademik Programlar',\n        engineering: 'Mühendislik',\n        medicine: 'Tıp',\n        business: 'İşletme',\n        arts: 'Sanat ve Beşeri Bilimler',\n        sciences: 'Fen Bilimleri',\n        law: 'Hukuk',\n        architecture: 'Mimarlık',\n        education: 'Eğitim',\n        duration: 'Süre',\n        language: 'Dil',\n        degree: 'Derece',\n        bachelor: 'Lisans',\n        master: 'Yüksek Lisans',\n        doctorate: 'Doktora'\n    },\n    testimonials: {\n        title: 'Öğrenci Başarı Hikayeleri',\n        subtitle: 'Başarılı Öğrencilerimizden Dinleyin',\n        readMore: 'Devamını Oku',\n        showLess: 'Daha Az Göster',\n        verified: 'Doğrulanmış Öğrenci',\n        graduate: 'Mezun',\n        currentStudent: 'Mevcut Öğrenci'\n    },\n    contact: {\n        title: 'İletişim',\n        subtitle: 'Eğitim Uzmanlarımızla İletişime Geçin',\n        getInTouch: 'İletişime Geç',\n        name: 'Ad Soyad',\n        email: 'E-posta Adresi',\n        phone: 'Telefon Numarası',\n        message: 'Mesaj',\n        subject: 'Konu',\n        send: 'Mesaj Gönder',\n        sending: 'Gönderiliyor...',\n        sent: 'Mesaj Başarıyla Gönderildi!',\n        error: 'Mesaj gönderilemedi. Lütfen tekrar deneyin.',\n        required: 'Bu alan zorunludur',\n        invalidEmail: 'Lütfen geçerli bir e-posta adresi girin',\n        office: 'Ofis Saatleri',\n        hours: 'Pazartesi - Cuma: 09:00 - 18:00',\n        emergency: '7/24 Acil Durum Desteği Mevcuttur'\n    },\n    footer: {\n        description: 'Kuzey Kıbrıs\\'ta uluslararası eğitim için güvenilir ortağınız. Başvurudan mezuniyete uzman rehberlik.',\n        quickLinks: 'Hızlı Bağlantılar',\n        services: 'Hizmetler',\n        contact: 'İletişim Bilgileri',\n        followUs: 'Bizi Takip Edin',\n        newsletter: 'Bülten',\n        newsletterDesc: 'Üniversiteler, programlar ve burslar hakkında en son güncellemeleri almak için abone olun.',\n        subscribe: 'Abone Ol',\n        subscribing: 'Abone Oluyor...',\n        subscribed: 'Başarıyla Abone Oldunuz!',\n        privacy: 'Gizlilik Politikası',\n        terms: 'Hizmet Şartları',\n        cookies: 'Çerez Politikası',\n        sitemap: 'Site Haritası',\n        allRightsReserved: 'Tüm hakları saklıdır.'\n    },\n    common: {\n        loading: 'Yükleniyor...',\n        error: 'Hata',\n        success: 'Başarılı',\n        warning: 'Uyarı',\n        info: 'Bilgi',\n        close: 'Kapat',\n        cancel: 'İptal',\n        confirm: 'Onayla',\n        save: 'Kaydet',\n        edit: 'Düzenle',\n        delete: 'Sil',\n        search: 'Ara',\n        filter: 'Filtrele',\n        sort: 'Sırala',\n        next: 'Sonraki',\n        previous: 'Önceki',\n        page: 'Sayfa',\n        of: '/',\n        showing: 'Gösteriliyor',\n        results: 'sonuç',\n        noResults: 'Sonuç bulunamadı',\n        tryAgain: 'Tekrar Dene',\n        learnMore: 'Daha Fazla Bilgi',\n        readMore: 'Devamını Oku',\n        showMore: 'Daha Fazla Göster',\n        showLess: 'Daha Az Göster',\n        viewAll: 'Tümünü Görüntüle',\n        backToTop: 'Başa Dön'\n    },\n    chatbot: {\n        title: 'Eğitim Asistanı',\n        placeholder: 'Üniversiteler, programlar, maliyetler hakkında sor...',\n        send: 'Gönder',\n        thinking: 'Düşünüyor...',\n        error: 'Üzgünüm, bir hatayla karşılaştım. Lütfen tekrar deneyin.',\n        retry: 'Tekrar Dene',\n        clear: 'Sohbeti Temizle',\n        minimize: 'Küçült',\n        maximize: 'Büyüt',\n        close: 'Kapat',\n        greeting: 'Merhaba! Eğitim yolculuğunuzda size yardımcı olmak için buradayım. Ne öğrenmek istiyorsunuz?',\n        suggestions: 'Önerilen Sorular',\n        typing: 'Yazıyor...',\n        offline: 'Çevrimdışı',\n        online: 'Çevrimiçi'\n    },\n    forms: {\n        firstName: 'Ad',\n        lastName: 'Soyad',\n        fullName: 'Ad Soyad',\n        email: 'E-posta Adresi',\n        phone: 'Telefon Numarası',\n        country: 'Ülke',\n        city: 'Şehir',\n        address: 'Adres',\n        zipCode: 'Posta Kodu',\n        dateOfBirth: 'Doğum Tarihi',\n        gender: 'Cinsiyet',\n        male: 'Erkek',\n        female: 'Kadın',\n        other: 'Diğer',\n        preferNotToSay: 'Belirtmek istemiyorum',\n        nationality: 'Uyruk',\n        passportNumber: 'Pasaport Numarası',\n        education: 'Eğitim Seviyesi',\n        highSchool: 'Lise',\n        bachelor: 'Lisans Derecesi',\n        master: 'Yüksek Lisans Derecesi',\n        doctorate: 'Doktora',\n        workExperience: 'İş Deneyimi',\n        englishLevel: 'İngilizce Seviyesi',\n        beginner: 'Başlangıç',\n        intermediate: 'Orta',\n        advanced: 'İleri',\n        native: 'Ana Dil',\n        interestedProgram: 'İlgilenilen Program',\n        interestedUniversity: 'İlgilenilen Üniversite',\n        startDate: 'Tercih Edilen Başlangıç Tarihi',\n        additionalInfo: 'Ek Bilgiler',\n        agreeTerms: 'Hizmet Şartlarını kabul ediyorum',\n        agreePrivacy: 'Gizlilik Politikasını kabul ediyorum',\n        agreeMarketing: 'Pazarlama iletişimi almayı kabul ediyorum',\n        submit: 'Gönder',\n        submitting: 'Gönderiliyor...',\n        submitted: 'Başarıyla Gönderildi!',\n        required: 'Zorunlu alan',\n        invalid: 'Geçersiz format',\n        tooShort: 'Çok kısa',\n        tooLong: 'Çok uzun',\n        passwordMismatch: 'Şifreler eşleşmiyor'\n    },\n    costs: {\n        title: 'Maliyetler ve Burslar',\n        subtitle: 'Mali Destek Seçenekleri ile Uygun Fiyatlı Eğitim',\n        tuitionFees: 'Öğrenim Ücretleri',\n        livingCosts: 'Yaşam Maliyetleri',\n        totalCost: 'Toplam Maliyet',\n        scholarships: 'Burslar',\n        financialAid: 'Mali Yardım',\n        paymentPlans: 'Ödeme Planları',\n        currency: 'USD',\n        perYear: 'yıllık',\n        perMonth: 'aylık',\n        accommodation: 'Konaklama',\n        food: 'Yemek ve Beslenme',\n        transportation: 'Ulaşım',\n        books: 'Kitap ve Malzemeler',\n        personal: 'Kişisel Harcamalar',\n        insurance: 'Sağlık Sigortası',\n        visa: 'Vize ve Göçmenlik',\n        other: 'Diğer Harcamalar',\n        meritScholarship: 'Başarı Bursu',\n        needBasedAid: 'İhtiyaç Temelli Yardım',\n        earlyBird: 'Erken Başvuru İndirimi',\n        siblingDiscount: 'Kardeş İndirimi',\n        calculate: 'Maliyetleri Hesapla',\n        getQuote: 'Teklif Al'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/locales/tr.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/next-themes","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsimple-test%2Fpage&page=%2Fsimple-test%2Fpage&appPaths=%2Fsimple-test%2Fpage&pagePath=private-next-app-dir%2Fsimple-test%2Fpage.tsx&appDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CNidhal%5CDocuments%5Caugment-projects%5Cforeingate_groupe%5Cforeingate-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();